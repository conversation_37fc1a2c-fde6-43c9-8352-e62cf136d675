{".class": "MypyFile", "_fullname": "webdriver_manager.core.http", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "HttpClient": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "webdriver_manager.core.http.HttpClient", "name": "HttpClient", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "webdriver_manager.core.http.HttpClient", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "webdriver_manager.core.http", "mro": ["webdriver_manager.core.http.HttpClient", "builtins.object"], "names": {".class": "SymbolTable", "get": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "url", "params", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "webdriver_manager.core.http.HttpClient.get", "name": "get", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "url", "params", "kwargs"], "arg_types": ["webdriver_manager.core.http.HttpClient", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get of HttpClient", "ret_type": {".class": "AnyType", "missing_import_name": "webdriver_manager.core.http.Response", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "validate_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["resp"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "webdriver_manager.core.http.HttpClient.validate_response", "name": "validate_response", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["resp"], "arg_types": [{".class": "AnyType", "missing_import_name": "webdriver_manager.core.http.requests", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "validate_response of HttpClient", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "webdriver_manager.core.http.HttpClient.validate_response", "name": "validate_response", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["resp"], "arg_types": [{".class": "AnyType", "missing_import_name": "webdriver_manager.core.http.requests", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "validate_response of HttpClient", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "webdriver_manager.core.http.HttpClient.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "webdriver_manager.core.http.HttpClient", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Response": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "webdriver_manager.core.http.Response", "name": "Response", "type": {".class": "AnyType", "missing_import_name": "webdriver_manager.core.http.Response", "source_any": null, "type_of_any": 3}}}, "WDMHttpClient": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["webdriver_manager.core.http.HttpClient"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "webdriver_manager.core.http.WDMHttpClient", "name": "WDMHttpClient", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "webdriver_manager.core.http.WDMHttpClient", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "webdriver_manager.core.http", "mro": ["webdriver_manager.core.http.WDMHttpClient", "webdriver_manager.core.http.HttpClient", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "webdriver_manager.core.http.WDMHttpClient.__init__", "name": "__init__", "type": null}}, "_ssl_verify": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "webdriver_manager.core.http.WDMHttpClient._ssl_verify", "name": "_ssl_verify", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "get": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "url", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "webdriver_manager.core.http.WDMHttpClient.get", "name": "get", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "url", "kwargs"], "arg_types": ["webdriver_manager.core.http.WDMHttpClient", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get of WDMHttpClient", "ret_type": {".class": "AnyType", "missing_import_name": "webdriver_manager.core.http.Response", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "webdriver_manager.core.http.WDMHttpClient.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "webdriver_manager.core.http.WDMHttpClient", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "webdriver_manager.core.http.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "webdriver_manager.core.http.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "webdriver_manager.core.http.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "webdriver_manager.core.http.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "webdriver_manager.core.http.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "webdriver_manager.core.http.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "exceptions": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "webdriver_manager.core.http.exceptions", "name": "exceptions", "type": {".class": "AnyType", "missing_import_name": "webdriver_manager.core.http.exceptions", "source_any": null, "type_of_any": 3}}}, "requests": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "webdriver_manager.core.http.requests", "name": "requests", "type": {".class": "AnyType", "missing_import_name": "webdriver_manager.core.http.requests", "source_any": null, "type_of_any": 3}}}, "ssl_verify": {".class": "SymbolTableNode", "cross_ref": "webdriver_manager.core.config.ssl_verify", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\webdriver_manager\\core\\http.py"}