{".class": "MypyFile", "_fullname": "http.cookiejar", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "ClassVar": {".class": "SymbolTableNode", "cross_ref": "typing.ClassVar", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Cookie": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "http.cookiejar.Cookie", "name": "<PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "http.cookiejar.Cookie", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "http.cookiejar", "mro": ["http.cookiejar.Cookie", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1], "arg_names": ["self", "version", "name", "value", "port", "port_specified", "domain", "domain_specified", "domain_initial_dot", "path", "path_specified", "secure", "expires", "discard", "comment", "comment_url", "rest", "rfc2109"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "http.cookiejar.Cookie.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1], "arg_names": ["self", "version", "name", "value", "port", "port_specified", "domain", "domain_specified", "domain_initial_dot", "path", "path_specified", "secure", "expires", "discard", "comment", "comment_url", "rest", "rfc2109"], "arg_types": ["http.cookiejar.Cookie", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", "builtins.str", "builtins.bool", "builtins.bool", "builtins.str", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of <PERSON><PERSON>", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "comment": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "http.cookiejar.Cookie.comment", "name": "comment", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "comment_url": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "http.cookiejar.Cookie.comment_url", "name": "comment_url", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "discard": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "http.cookiejar.Cookie.discard", "name": "discard", "type": "builtins.bool"}}, "domain": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "http.cookiejar.Cookie.domain", "name": "domain", "type": "builtins.str"}}, "domain_initial_dot": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "http.cookiejar.Cookie.domain_initial_dot", "name": "domain_initial_dot", "type": "builtins.bool"}}, "domain_specified": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "http.cookiejar.Cookie.domain_specified", "name": "domain_specified", "type": "builtins.bool"}}, "expires": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "http.cookiejar.Cookie.expires", "name": "expires", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "get_nonstandard_attr": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "http.cookiejar.Cookie.get_nonstandard_attr", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "http.cookiejar.Cookie.get_nonstandard_attr", "name": "get_nonstandard_attr", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "name"], "arg_types": ["http.cookiejar.Cookie", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_nonstandard_attr of <PERSON><PERSON>", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "http.cookiejar.Cookie.get_nonstandard_attr", "name": "get_nonstandard_attr", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "name"], "arg_types": ["http.cookiejar.Cookie", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_nonstandard_attr of <PERSON><PERSON>", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "default"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "http.cookiejar.Cookie.get_nonstandard_attr", "name": "get_nonstandard_attr", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "default"], "arg_types": ["http.cookiejar.Cookie", "builtins.str", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "http.cookiejar._T", "id": -1, "name": "_T", "namespace": "http.cookiejar.Cookie.get_nonstandard_attr", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_nonstandard_attr of <PERSON><PERSON>", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "http.cookiejar._T", "id": -1, "name": "_T", "namespace": "http.cookiejar.Cookie.get_nonstandard_attr", "upper_bound": "builtins.object", "values": [], "variance": 0}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "http.cookiejar._T", "id": -1, "name": "_T", "namespace": "http.cookiejar.Cookie.get_nonstandard_attr", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "http.cookiejar.Cookie.get_nonstandard_attr", "name": "get_nonstandard_attr", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "default"], "arg_types": ["http.cookiejar.Cookie", "builtins.str", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "http.cookiejar._T", "id": -1, "name": "_T", "namespace": "http.cookiejar.Cookie.get_nonstandard_attr", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_nonstandard_attr of <PERSON><PERSON>", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "http.cookiejar._T", "id": -1, "name": "_T", "namespace": "http.cookiejar.Cookie.get_nonstandard_attr", "upper_bound": "builtins.object", "values": [], "variance": 0}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "http.cookiejar._T", "id": -1, "name": "_T", "namespace": "http.cookiejar.Cookie.get_nonstandard_attr", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "name"], "arg_types": ["http.cookiejar.Cookie", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_nonstandard_attr of <PERSON><PERSON>", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "default"], "arg_types": ["http.cookiejar.Cookie", "builtins.str", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "http.cookiejar._T", "id": -1, "name": "_T", "namespace": "http.cookiejar.Cookie.get_nonstandard_attr", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_nonstandard_attr of <PERSON><PERSON>", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "http.cookiejar._T", "id": -1, "name": "_T", "namespace": "http.cookiejar.Cookie.get_nonstandard_attr", "upper_bound": "builtins.object", "values": [], "variance": 0}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "http.cookiejar._T", "id": -1, "name": "_T", "namespace": "http.cookiejar.Cookie.get_nonstandard_attr", "upper_bound": "builtins.object", "values": [], "variance": 0}]}]}}}, "has_nonstandard_attr": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "http.cookiejar.Cookie.has_nonstandard_attr", "name": "has_nonstandard_attr", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "name"], "arg_types": ["http.cookiejar.Cookie", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "has_nonstandard_attr of Cookie", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_expired": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "now"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "http.cookiejar.Cookie.is_expired", "name": "is_expired", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "now"], "arg_types": ["http.cookiejar.Cookie", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_expired of <PERSON><PERSON>", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "http.cookiejar.Cookie.name", "name": "name", "type": "builtins.str"}}, "path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "http.cookiejar.Cookie.path", "name": "path", "type": "builtins.str"}}, "path_specified": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "http.cookiejar.Cookie.path_specified", "name": "path_specified", "type": "builtins.bool"}}, "port": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "http.cookiejar.Cookie.port", "name": "port", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "port_specified": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "http.cookiejar.Cookie.port_specified", "name": "port_specified", "type": "builtins.bool"}}, "rfc2109": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "http.cookiejar.Cookie.rfc2109", "name": "rfc2109", "type": "builtins.bool"}}, "secure": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "http.cookiejar.Cookie.secure", "name": "secure", "type": "builtins.bool"}}, "set_nonstandard_attr": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "http.cookiejar.Cookie.set_nonstandard_attr", "name": "set_nonstandard_attr", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "value"], "arg_types": ["http.cookiejar.Cookie", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_nonstandard_attr of <PERSON><PERSON>", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "http.cookiejar.Cookie.value", "name": "value", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "version": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "http.cookiejar.Cookie.version", "name": "version", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "http.cookiejar.Cookie.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "http.cookiejar.Cookie", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CookieJar": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "http.cookiejar.CookieJar", "name": "<PERSON><PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "http.cookiejar.CookieJar", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "http.cookiejar", "mro": ["http.cookiejar.CookieJar", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "policy"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "http.cookiejar.CookieJar.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "policy"], "arg_types": ["http.cookiejar.CookieJar", {".class": "UnionType", "items": ["http.cookiejar.CookiePolicy", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Cookie<PERSON><PERSON>", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__iter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "http.cookiejar.CookieJar.__iter__", "name": "__iter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["http.cookiejar.CookieJar"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__iter__ of <PERSON><PERSON><PERSON><PERSON>", "ret_type": {".class": "Instance", "args": ["http.cookiejar.Cookie"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__len__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "http.cookiejar.CookieJar.__len__", "name": "__len__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["http.cookiejar.CookieJar"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__len__ of CookieJar", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "add_cookie_header": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "http.cookiejar.CookieJar.add_cookie_header", "name": "add_cookie_header", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "request"], "arg_types": ["http.cookiejar.CookieJar", "urllib.request.Request"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_cookie_header of CookieJar", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "clear": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "domain", "path", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "http.cookiejar.CookieJar.clear", "name": "clear", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "domain", "path", "name"], "arg_types": ["http.cookiejar.CookieJar", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "clear of <PERSON><PERSON><PERSON><PERSON>", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "clear_expired_cookies": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "http.cookiejar.CookieJar.clear_expired_cookies", "name": "clear_expired_cookies", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["http.cookiejar.CookieJar"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "clear_expired_cookies of CookieJar", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "clear_session_cookies": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "http.cookiejar.CookieJar.clear_session_cookies", "name": "clear_session_cookies", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["http.cookiejar.CookieJar"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "clear_session_cookies of CookieJar", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "domain_re": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "http.cookiejar.CookieJar.domain_re", "name": "domain_re", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "dots_re": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "http.cookiejar.CookieJar.dots_re", "name": "dots_re", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "extract_cookies": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "response", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "http.cookiejar.CookieJar.extract_cookies", "name": "extract_cookies", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "response", "request"], "arg_types": ["http.cookiejar.CookieJar", "http.client.HTTPResponse", "urllib.request.Request"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "extract_cookies of CookieJar", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "magic_re": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "http.cookiejar.CookieJar.magic_re", "name": "magic_re", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "make_cookies": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "response", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "http.cookiejar.CookieJar.make_cookies", "name": "make_cookies", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "response", "request"], "arg_types": ["http.cookiejar.CookieJar", "http.client.HTTPResponse", "urllib.request.Request"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "make_cookies of CookieJar", "ret_type": {".class": "Instance", "args": ["http.cookiejar.Cookie"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "non_word_re": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "http.cookiejar.CookieJar.non_word_re", "name": "non_word_re", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "quote_re": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "http.cookiejar.CookieJar.quote_re", "name": "quote_re", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "set_cookie": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "cookie"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "http.cookiejar.CookieJar.set_cookie", "name": "set_cookie", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "cookie"], "arg_types": ["http.cookiejar.CookieJar", "http.cookiejar.Cookie"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_cookie of CookieJar", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_cookie_if_ok": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "cookie", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "http.cookiejar.CookieJar.set_cookie_if_ok", "name": "set_cookie_if_ok", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "cookie", "request"], "arg_types": ["http.cookiejar.CookieJar", "http.cookiejar.Cookie", "urllib.request.Request"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_cookie_if_ok of CookieJar", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_policy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "policy"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "http.cookiejar.CookieJar.set_policy", "name": "set_policy", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "policy"], "arg_types": ["http.cookiejar.CookieJar", "http.cookiejar.CookiePolicy"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_policy of <PERSON><PERSON><PERSON><PERSON>", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "strict_domain_re": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "http.cookiejar.CookieJar.strict_domain_re", "name": "strict_domain_re", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "http.cookiejar.CookieJar.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "http.cookiejar.CookieJar", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CookiePolicy": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "http.cookiejar.CookiePolicy", "name": "<PERSON>ie<PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "http.cookiejar.CookiePolicy", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "http.cookiejar", "mro": ["http.cookiejar.CookiePolicy", "builtins.object"], "names": {".class": "SymbolTable", "domain_return_ok": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "domain", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "http.cookiejar.CookiePolicy.domain_return_ok", "name": "domain_return_ok", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "domain", "request"], "arg_types": ["http.cookiejar.CookiePolicy", "builtins.str", "urllib.request.Request"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "domain_return_ok of CookiePolicy", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "hide_cookie2": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "http.cookiejar.CookiePolicy.hide_cookie2", "name": "hide_cookie2", "type": "builtins.bool"}}, "netscape": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "http.cookiejar.CookiePolicy.netscape", "name": "netscape", "type": "builtins.bool"}}, "path_return_ok": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "path", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "http.cookiejar.CookiePolicy.path_return_ok", "name": "path_return_ok", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "path", "request"], "arg_types": ["http.cookiejar.CookiePolicy", "builtins.str", "urllib.request.Request"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "path_return_ok of CookiePolicy", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "return_ok": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "cookie", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "http.cookiejar.CookiePolicy.return_ok", "name": "return_ok", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "cookie", "request"], "arg_types": ["http.cookiejar.CookiePolicy", "http.cookiejar.Cookie", "urllib.request.Request"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "return_ok of CookiePolicy", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "rfc2965": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "http.cookiejar.CookiePolicy.rfc2965", "name": "rfc2965", "type": "builtins.bool"}}, "set_ok": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "cookie", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "http.cookiejar.CookiePolicy.set_ok", "name": "set_ok", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "cookie", "request"], "arg_types": ["http.cookiejar.CookiePolicy", "http.cookiejar.Cookie", "urllib.request.Request"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_ok of CookiePolicy", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "http.cookiejar.CookiePolicy.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "http.cookiejar.CookiePolicy", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DefaultCookiePolicy": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["http.cookiejar.CookiePolicy"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "http.cookiejar.DefaultCookiePolicy", "name": "DefaultCookiePolicy", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "http.cookiejar.DefaultCookiePolicy", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "http.cookiejar", "mro": ["http.cookiejar.DefaultCookiePolicy", "http.cookiejar.CookiePolicy", "builtins.object"], "names": {".class": "SymbolTable", "DomainLiberal": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "http.cookiejar.DefaultCookiePolicy.DomainLiberal", "name": "DomainLiberal", "type": "builtins.int"}}, "DomainRFC2965Match": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "http.cookiejar.DefaultCookiePolicy.DomainRFC2965Match", "name": "DomainRFC2965Match", "type": "builtins.int"}}, "DomainStrict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "http.cookiejar.DefaultCookiePolicy.DomainStrict", "name": "DomainStrict", "type": "builtins.int"}}, "DomainStrictNoDots": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "http.cookiejar.DefaultCookiePolicy.DomainStrictNoDots", "name": "DomainStrictNoDots", "type": "builtins.int"}}, "DomainStrictNonDomain": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "http.cookiejar.DefaultCookiePolicy.DomainStrictNonDomain", "name": "DomainStrictNonDomain", "type": "builtins.int"}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "blocked_domains", "allowed_domains", "netscape", "rfc2965", "rfc2109_as_netscape", "hide_cookie2", "strict_domain", "strict_rfc2965_unverifiable", "strict_ns_unverifiable", "strict_ns_domain", "strict_ns_set_initial_dollar", "strict_ns_set_path", "secure_protocols"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "http.cookiejar.DefaultCookiePolicy.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "blocked_domains", "allowed_domains", "netscape", "rfc2965", "rfc2109_as_netscape", "hide_cookie2", "strict_domain", "strict_rfc2965_unverifiable", "strict_ns_unverifiable", "strict_ns_domain", "strict_ns_set_initial_dollar", "strict_ns_set_path", "secure_protocols"], "arg_types": ["http.cookiejar.DefaultCookiePolicy", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.int", "builtins.bool", "builtins.bool", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of DefaultCookiePolicy", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "allowed_domains": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "http.cookiejar.DefaultCookiePolicy.allowed_domains", "name": "allowed_domains", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["http.cookiejar.DefaultCookiePolicy"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "allowed_domains of DefaultCookiePolicy", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "blocked_domains": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "http.cookiejar.DefaultCookiePolicy.blocked_domains", "name": "blocked_domains", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["http.cookiejar.DefaultCookiePolicy"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "blocked_domains of DefaultCookiePolicy", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_blocked": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "domain"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "http.cookiejar.DefaultCookiePolicy.is_blocked", "name": "is_blocked", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "domain"], "arg_types": ["http.cookiejar.DefaultCookiePolicy", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_blocked of DefaultCookiePolicy", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_not_allowed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "domain"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "http.cookiejar.DefaultCookiePolicy.is_not_allowed", "name": "is_not_allowed", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "domain"], "arg_types": ["http.cookiejar.DefaultCookiePolicy", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_not_allowed of DefaultCookiePolicy", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "return_ok_domain": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "cookie", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "http.cookiejar.DefaultCookiePolicy.return_ok_domain", "name": "return_ok_domain", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "cookie", "request"], "arg_types": ["http.cookiejar.DefaultCookiePolicy", "http.cookiejar.Cookie", "urllib.request.Request"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "return_ok_domain of DefaultCookiePolicy", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "return_ok_expires": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "cookie", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "http.cookiejar.DefaultCookiePolicy.return_ok_expires", "name": "return_ok_expires", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "cookie", "request"], "arg_types": ["http.cookiejar.DefaultCookiePolicy", "http.cookiejar.Cookie", "urllib.request.Request"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "return_ok_expires of DefaultCookiePolicy", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "return_ok_port": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "cookie", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "http.cookiejar.DefaultCookiePolicy.return_ok_port", "name": "return_ok_port", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "cookie", "request"], "arg_types": ["http.cookiejar.DefaultCookiePolicy", "http.cookiejar.Cookie", "urllib.request.Request"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "return_ok_port of DefaultCookiePolicy", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "return_ok_secure": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "cookie", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "http.cookiejar.DefaultCookiePolicy.return_ok_secure", "name": "return_ok_secure", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "cookie", "request"], "arg_types": ["http.cookiejar.DefaultCookiePolicy", "http.cookiejar.Cookie", "urllib.request.Request"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "return_ok_secure of DefaultCookiePolicy", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "return_ok_verifiability": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "cookie", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "http.cookiejar.DefaultCookiePolicy.return_ok_verifiability", "name": "return_ok_verifiability", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "cookie", "request"], "arg_types": ["http.cookiejar.DefaultCookiePolicy", "http.cookiejar.Cookie", "urllib.request.Request"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "return_ok_verifiability of DefaultCookiePolicy", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "return_ok_version": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "cookie", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "http.cookiejar.DefaultCookiePolicy.return_ok_version", "name": "return_ok_version", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "cookie", "request"], "arg_types": ["http.cookiejar.DefaultCookiePolicy", "http.cookiejar.Cookie", "urllib.request.Request"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "return_ok_version of DefaultCookiePolicy", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "rfc2109_as_netscape": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "http.cookiejar.DefaultCookiePolicy.rfc2109_as_netscape", "name": "rfc2109_as_netscape", "type": "builtins.bool"}}, "set_allowed_domains": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "allowed_domains"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "http.cookiejar.DefaultCookiePolicy.set_allowed_domains", "name": "set_allowed_domains", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "allowed_domains"], "arg_types": ["http.cookiejar.DefaultCookiePolicy", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_allowed_domains of DefaultCookiePolicy", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_blocked_domains": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "blocked_domains"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "http.cookiejar.DefaultCookiePolicy.set_blocked_domains", "name": "set_blocked_domains", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "blocked_domains"], "arg_types": ["http.cookiejar.DefaultCookiePolicy", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_blocked_domains of DefaultCookiePolicy", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_ok_domain": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "cookie", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "http.cookiejar.DefaultCookiePolicy.set_ok_domain", "name": "set_ok_domain", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "cookie", "request"], "arg_types": ["http.cookiejar.DefaultCookiePolicy", "http.cookiejar.Cookie", "urllib.request.Request"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_ok_domain of DefaultCookiePolicy", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_ok_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "cookie", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "http.cookiejar.DefaultCookiePolicy.set_ok_name", "name": "set_ok_name", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "cookie", "request"], "arg_types": ["http.cookiejar.DefaultCookiePolicy", "http.cookiejar.Cookie", "urllib.request.Request"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_ok_name of DefaultCookiePolicy", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_ok_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "cookie", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "http.cookiejar.DefaultCookiePolicy.set_ok_path", "name": "set_ok_path", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "cookie", "request"], "arg_types": ["http.cookiejar.DefaultCookiePolicy", "http.cookiejar.Cookie", "urllib.request.Request"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_ok_path of DefaultCookiePolicy", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_ok_port": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "cookie", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "http.cookiejar.DefaultCookiePolicy.set_ok_port", "name": "set_ok_port", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "cookie", "request"], "arg_types": ["http.cookiejar.DefaultCookiePolicy", "http.cookiejar.Cookie", "urllib.request.Request"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_ok_port of DefaultCookiePolicy", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_ok_verifiability": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "cookie", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "http.cookiejar.DefaultCookiePolicy.set_ok_verifiability", "name": "set_ok_verifiability", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "cookie", "request"], "arg_types": ["http.cookiejar.DefaultCookiePolicy", "http.cookiejar.Cookie", "urllib.request.Request"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_ok_verifiability of DefaultCookiePolicy", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_ok_version": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "cookie", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "http.cookiejar.DefaultCookiePolicy.set_ok_version", "name": "set_ok_version", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "cookie", "request"], "arg_types": ["http.cookiejar.DefaultCookiePolicy", "http.cookiejar.Cookie", "urllib.request.Request"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_ok_version of DefaultCookiePolicy", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "strict_domain": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "http.cookiejar.DefaultCookiePolicy.strict_domain", "name": "strict_domain", "type": "builtins.bool"}}, "strict_ns_domain": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "http.cookiejar.DefaultCookiePolicy.strict_ns_domain", "name": "strict_ns_domain", "type": "builtins.int"}}, "strict_ns_set_initial_dollar": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "http.cookiejar.DefaultCookiePolicy.strict_ns_set_initial_dollar", "name": "strict_ns_set_initial_dollar", "type": "builtins.bool"}}, "strict_ns_set_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "http.cookiejar.DefaultCookiePolicy.strict_ns_set_path", "name": "strict_ns_set_path", "type": "builtins.bool"}}, "strict_ns_unverifiable": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "http.cookiejar.DefaultCookiePolicy.strict_ns_unverifiable", "name": "strict_ns_unverifiable", "type": "builtins.bool"}}, "strict_rfc2965_unverifiable": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "http.cookiejar.DefaultCookiePolicy.strict_rfc2965_unverifiable", "name": "strict_rfc2965_unverifiable", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "http.cookiejar.DefaultCookiePolicy.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "http.cookiejar.DefaultCookiePolicy", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FileCookieJar": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["http.cookiejar.CookieJar"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "http.cookiejar.FileCookieJar", "name": "FileCookieJar", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "http.cookiejar.FileCookieJar", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "http.cookiejar", "mro": ["http.cookiejar.FileCookieJar", "http.cookiejar.CookieJar", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "filename", "delayload", "policy"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "http.cookiejar.FileCookieJar.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "filename", "delayload", "policy"], "arg_types": ["http.cookiejar.FileCookieJar", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.StrPath"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", {".class": "UnionType", "items": ["http.cookiejar.CookiePolicy", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of FileCookieJar", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "delayload": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "http.cookiejar.FileCookieJar.delayload", "name": "delayload", "type": "builtins.bool"}}, "filename": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "http.cookiejar.FileCookieJar.filename", "name": "filename", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "load": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "filename", "ignore_discard", "ignore_expires"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "http.cookiejar.FileCookieJar.load", "name": "load", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "filename", "ignore_discard", "ignore_expires"], "arg_types": ["http.cookiejar.FileCookieJar", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "load of FileCookieJar", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "revert": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "filename", "ignore_discard", "ignore_expires"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "http.cookiejar.FileCookieJar.revert", "name": "revert", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "filename", "ignore_discard", "ignore_expires"], "arg_types": ["http.cookiejar.FileCookieJar", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "revert of FileCookieJar", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "save": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "filename", "ignore_discard", "ignore_expires"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "http.cookiejar.FileCookieJar.save", "name": "save", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "filename", "ignore_discard", "ignore_expires"], "arg_types": ["http.cookiejar.FileCookieJar", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "save of FileCookieJar", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "http.cookiejar.FileCookieJar.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "http.cookiejar.FileCookieJar", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HTTPResponse": {".class": "SymbolTableNode", "cross_ref": "http.client.HTTPResponse", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Iterator": {".class": "SymbolTableNode", "cross_ref": "typing.Iterator", "kind": "Gdef", "module_hidden": true, "module_public": false}, "LWPCookieJar": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["http.cookiejar.FileCookieJar"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "http.cookiejar.LWPCookieJar", "name": "LWPCookieJar", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "http.cookiejar.LWPCookieJar", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "http.cookiejar", "mro": ["http.cookiejar.LWPCookieJar", "http.cookiejar.FileCookieJar", "http.cookiejar.CookieJar", "builtins.object"], "names": {".class": "SymbolTable", "as_lwp_str": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "ignore_discard", "ignore_expires"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "http.cookiejar.LWPCookieJar.as_lwp_str", "name": "as_lwp_str", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "ignore_discard", "ignore_expires"], "arg_types": ["http.cookiejar.LWPCookieJar", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_lwp_str of LWPCookieJar", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "http.cookiejar.LWPCookieJar.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "http.cookiejar.LWPCookieJar", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "LoadError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.OSError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "http.cookiejar.LoadError", "name": "LoadError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "http.cookiejar.LoadError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "http.cookiejar", "mro": ["http.cookiejar.LoadError", "builtins.OSError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "http.cookiejar.LoadError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "http.cookiejar.LoadError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MozillaCookieJar": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["http.cookiejar.FileCookieJar"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "http.cookiejar.MozillaCookieJar", "name": "MozillaCookieJar", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "http.cookiejar.MozillaCookieJar", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "http.cookiejar", "mro": ["http.cookiejar.MozillaCookieJar", "http.cookiejar.FileCookieJar", "http.cookiejar.CookieJar", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "http.cookiejar.MozillaCookieJar.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "http.cookiejar.MozillaCookieJar", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Pattern": {".class": "SymbolTableNode", "cross_ref": "<PERSON><PERSON>", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Request": {".class": "SymbolTableNode", "cross_ref": "urllib.request.Request", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef", "module_hidden": true, "module_public": false}, "StrPath": {".class": "SymbolTableNode", "cross_ref": "_typeshed.StrPath", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_T": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "http.cookiejar._T", "name": "_T", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "http.cookiejar.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "http.cookiejar.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "http.cookiejar.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "http.cookiejar.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "http.cookiejar.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "http.cookiejar.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "http.cookiejar.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "overload": {".class": "SymbolTableNode", "cross_ref": "typing.overload", "kind": "Gdef", "module_hidden": true, "module_public": false}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.mypy-type-checker-2025.2.0\\bundled\\libs\\mypy\\typeshed\\stdlib\\http\\cookiejar.pyi"}