{".class": "MypyFile", "_fullname": "webdriver_manager.core.driver_cache", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "DEFAULT_PROJECT_ROOT_CACHE_PATH": {".class": "SymbolTableNode", "cross_ref": "webdriver_manager.core.constants.DEFAULT_PROJECT_ROOT_CACHE_PATH", "kind": "Gdef"}, "DEFAULT_USER_HOME_CACHE_PATH": {".class": "SymbolTableNode", "cross_ref": "webdriver_manager.core.constants.DEFAULT_USER_HOME_CACHE_PATH", "kind": "Gdef"}, "Driver": {".class": "SymbolTableNode", "cross_ref": "webdriver_manager.core.driver.Driver", "kind": "Gdef"}, "DriverCacheManager": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "webdriver_manager.core.driver_cache.DriverCacheManager", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "webdriver_manager.core.driver_cache.DriverCacheManager", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "webdriver_manager.core.driver_cache", "mro": ["webdriver_manager.core.driver_cache.DriverCacheManager", "builtins.object"], "names": {".class": "SymbolTable", "__get_binary": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "files", "driver_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "webdriver_manager.core.driver_cache.DriverCacheManager.__get_binary", "name": "__get_binary", "type": null}}, "__get_metadata_key": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "driver"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "webdriver_manager.core.driver_cache.DriverCacheManager.__get_metadata_key", "name": "__get_metadata_key", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "driver"], "arg_types": ["webdriver_manager.core.driver_cache.DriverCacheManager", "webdriver_manager.core.driver.Driver"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__get_metadata_key of DriverCacheManager", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__get_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "driver"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "webdriver_manager.core.driver_cache.DriverCacheManager.__get_path", "name": "__get_path", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "driver"], "arg_types": ["webdriver_manager.core.driver_cache.DriverCacheManager", "webdriver_manager.core.driver.Driver"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__get_path of DriverCacheManager", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "root_dir", "valid_range", "file_manager"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "webdriver_manager.core.driver_cache.DriverCacheManager.__init__", "name": "__init__", "type": null}}, "__is_valid": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "driver_info"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "webdriver_manager.core.driver_cache.DriverCacheManager.__is_valid", "name": "__is_valid", "type": null}}, "__save_metadata": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "driver", "binary_path", "date"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "webdriver_manager.core.driver_cache.DriverCacheManager.__save_metadata", "name": "__save_metadata", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "driver", "binary_path", "date"], "arg_types": ["webdriver_manager.core.driver_cache.DriverCacheManager", "webdriver_manager.core.driver.Driver", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__save_metadata of DriverCacheManager", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_cache_key_driver_version": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "webdriver_manager.core.driver_cache.DriverCacheManager._cache_key_driver_version", "name": "_cache_key_driver_version", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_cache_valid_days_range": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "webdriver_manager.core.driver_cache.DriverCacheManager._cache_valid_days_range", "name": "_cache_valid_days_range", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_date_format": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "webdriver_manager.core.driver_cache.DriverCacheManager._date_format", "name": "_date_format", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_driver_binary_path": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "webdriver_manager.core.driver_cache.DriverCacheManager._driver_binary_path", "name": "_driver_binary_path", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_drivers_directory": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "webdriver_manager.core.driver_cache.DriverCacheManager._drivers_directory", "name": "_drivers_directory", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_drivers_json_path": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "webdriver_manager.core.driver_cache.DriverCacheManager._drivers_json_path", "name": "_drivers_json_path", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_drivers_root": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "webdriver_manager.core.driver_cache.DriverCacheManager._drivers_root", "name": "_drivers_root", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_file_manager": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "webdriver_manager.core.driver_cache.DriverCacheManager._file_manager", "name": "_file_manager", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_metadata_key": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "webdriver_manager.core.driver_cache.DriverCacheManager._metadata_key", "name": "_metadata_key", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_os_system_manager": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "webdriver_manager.core.driver_cache.DriverCacheManager._os_system_manager", "name": "_os_system_manager", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_root_dir": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "webdriver_manager.core.driver_cache.DriverCacheManager._root_dir", "name": "_root_dir", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "find_driver": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "driver"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "webdriver_manager.core.driver_cache.DriverCacheManager.find_driver", "name": "find_driver", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "driver"], "arg_types": ["webdriver_manager.core.driver_cache.DriverCacheManager", "webdriver_manager.core.driver.Driver"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "find_driver of DriverCacheManager", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_cache_key_driver_version": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "driver"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "webdriver_manager.core.driver_cache.DriverCacheManager.get_cache_key_driver_version", "name": "get_cache_key_driver_version", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "driver"], "arg_types": ["webdriver_manager.core.driver_cache.DriverCacheManager", "webdriver_manager.core.driver.Driver"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_cache_key_driver_version of DriverCacheManager", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_os_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "webdriver_manager.core.driver_cache.DriverCacheManager.get_os_type", "name": "get_os_type", "type": null}}, "load_metadata_content": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "webdriver_manager.core.driver_cache.DriverCacheManager.load_metadata_content", "name": "load_metadata_content", "type": null}}, "save_archive_file": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "file", "path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "webdriver_manager.core.driver_cache.DriverCacheManager.save_archive_file", "name": "save_archive_file", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "file", "path"], "arg_types": ["webdriver_manager.core.driver_cache.DriverCacheManager", "webdriver_manager.core.file_manager.File", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "save_archive_file of DriverCacheManager", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "save_file_to_cache": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "driver", "file"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "webdriver_manager.core.driver_cache.DriverCacheManager.save_file_to_cache", "name": "save_file_to_cache", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "driver", "file"], "arg_types": ["webdriver_manager.core.driver_cache.DriverCacheManager", "webdriver_manager.core.driver.Driver", "webdriver_manager.core.file_manager.File"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "save_file_to_cache of DriverCacheManager", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "unpack_archive": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "archive", "path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "webdriver_manager.core.driver_cache.DriverCacheManager.unpack_archive", "name": "unpack_archive", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "webdriver_manager.core.driver_cache.DriverCacheManager.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "webdriver_manager.core.driver_cache.DriverCacheManager", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "File": {".class": "SymbolTableNode", "cross_ref": "webdriver_manager.core.file_manager.File", "kind": "Gdef"}, "FileManager": {".class": "SymbolTableNode", "cross_ref": "webdriver_manager.core.file_manager.FileManager", "kind": "Gdef"}, "OperationSystemManager": {".class": "SymbolTableNode", "cross_ref": "webdriver_manager.core.os_manager.OperationSystemManager", "kind": "Gdef"}, "ROOT_FOLDER_NAME": {".class": "SymbolTableNode", "cross_ref": "webdriver_manager.core.constants.ROOT_FOLDER_NAME", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "webdriver_manager.core.driver_cache.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "webdriver_manager.core.driver_cache.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "webdriver_manager.core.driver_cache.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "webdriver_manager.core.driver_cache.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "webdriver_manager.core.driver_cache.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "webdriver_manager.core.driver_cache.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "datetime": {".class": "SymbolTableNode", "cross_ref": "datetime", "kind": "Gdef"}, "get_date_diff": {".class": "SymbolTableNode", "cross_ref": "webdriver_manager.core.utils.get_date_diff", "kind": "Gdef"}, "get_xdist_worker_id": {".class": "SymbolTableNode", "cross_ref": "webdriver_manager.core.config.get_xdist_worker_id", "kind": "Gdef"}, "json": {".class": "SymbolTableNode", "cross_ref": "json", "kind": "Gdef"}, "log": {".class": "SymbolTableNode", "cross_ref": "webdriver_manager.core.logger.log", "kind": "Gdef"}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "wdm_local": {".class": "SymbolTableNode", "cross_ref": "webdriver_manager.core.config.wdm_local", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\webdriver_manager\\core\\driver_cache.py"}