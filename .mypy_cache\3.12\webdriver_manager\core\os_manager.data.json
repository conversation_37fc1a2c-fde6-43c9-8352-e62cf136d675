{".class": "MypyFile", "_fullname": "webdriver_manager.core.os_manager", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ChromeType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "webdriver_manager.core.os_manager.ChromeType", "name": "ChromeType", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "webdriver_manager.core.os_manager.ChromeType", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "webdriver_manager.core.os_manager", "mro": ["webdriver_manager.core.os_manager.ChromeType", "builtins.object"], "names": {".class": "SymbolTable", "BRAVE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "webdriver_manager.core.os_manager.ChromeType.BRAVE", "name": "BRAVE", "type": "builtins.str"}}, "CHROMIUM": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "webdriver_manager.core.os_manager.ChromeType.CHROMIUM", "name": "CHROMIUM", "type": "builtins.str"}}, "GOOGLE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "webdriver_manager.core.os_manager.ChromeType.GOOGLE", "name": "GOOGLE", "type": "builtins.str"}}, "MSEDGE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "webdriver_manager.core.os_manager.ChromeType.MSEDGE", "name": "MSEDGE", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "webdriver_manager.core.os_manager.ChromeType.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "webdriver_manager.core.os_manager.ChromeType", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "OSType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "webdriver_manager.core.os_manager.OSType", "name": "OSType", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "webdriver_manager.core.os_manager.OSType", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "webdriver_manager.core.os_manager", "mro": ["webdriver_manager.core.os_manager.OSType", "builtins.object"], "names": {".class": "SymbolTable", "LINUX": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "webdriver_manager.core.os_manager.OSType.LINUX", "name": "LINUX", "type": "builtins.str"}}, "MAC": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "webdriver_manager.core.os_manager.OSType.MAC", "name": "MAC", "type": "builtins.str"}}, "WIN": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "webdriver_manager.core.os_manager.OSType.WIN", "name": "WIN", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "webdriver_manager.core.os_manager.OSType.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "webdriver_manager.core.os_manager.OSType", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "OperationSystemManager": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "webdriver_manager.core.os_manager.OperationSystemManager", "name": "OperationSystemManager", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "webdriver_manager.core.os_manager.OperationSystemManager", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "webdriver_manager.core.os_manager", "mro": ["webdriver_manager.core.os_manager.OperationSystemManager", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "os_type"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "webdriver_manager.core.os_manager.OperationSystemManager.__init__", "name": "__init__", "type": null}}, "_os_type": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "webdriver_manager.core.os_manager.OperationSystemManager._os_type", "name": "_os_type", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "get_browser_version_from_os": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "browser_type"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "webdriver_manager.core.os_manager.OperationSystemManager.get_browser_version_from_os", "name": "get_browser_version_from_os", "type": null}}, "get_os_architecture": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "webdriver_manager.core.os_manager.OperationSystemManager.get_os_architecture", "name": "get_os_architecture", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "webdriver_manager.core.os_manager.OperationSystemManager.get_os_architecture", "name": "get_os_architecture", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_os_architecture of OperationSystemManager", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_os_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "webdriver_manager.core.os_manager.OperationSystemManager.get_os_name", "name": "get_os_name", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "webdriver_manager.core.os_manager.OperationSystemManager.get_os_name", "name": "get_os_name", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_os_name of OperationSystemManager", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_os_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "webdriver_manager.core.os_manager.OperationSystemManager.get_os_type", "name": "get_os_type", "type": null}}, "is_arch": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["os_sys_type"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "webdriver_manager.core.os_manager.OperationSystemManager.is_arch", "name": "is_arch", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "webdriver_manager.core.os_manager.OperationSystemManager.is_arch", "name": "is_arch", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["os_sys_type"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_arch of OperationSystemManager", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "is_mac_os": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["os_sys_type"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "webdriver_manager.core.os_manager.OperationSystemManager.is_mac_os", "name": "is_mac_os", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "webdriver_manager.core.os_manager.OperationSystemManager.is_mac_os", "name": "is_mac_os", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["os_sys_type"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_mac_os of OperationSystemManager", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "webdriver_manager.core.os_manager.OperationSystemManager.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "webdriver_manager.core.os_manager.OperationSystemManager", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PATTERN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "webdriver_manager.core.os_manager.PATTERN", "name": "PATTERN", "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "webdriver_manager.core.os_manager.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "webdriver_manager.core.os_manager.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "webdriver_manager.core.os_manager.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "webdriver_manager.core.os_manager.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "webdriver_manager.core.os_manager.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "webdriver_manager.core.os_manager.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "linux_browser_apps_to_cmd": {".class": "SymbolTableNode", "cross_ref": "webdriver_manager.core.utils.linux_browser_apps_to_cmd", "kind": "Gdef"}, "platform": {".class": "SymbolTableNode", "cross_ref": "platform", "kind": "Gdef"}, "read_version_from_cmd": {".class": "SymbolTableNode", "cross_ref": "webdriver_manager.core.utils.read_version_from_cmd", "kind": "Gdef"}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef"}, "windows_browser_apps_to_cmd": {".class": "SymbolTableNode", "cross_ref": "webdriver_manager.core.utils.windows_browser_apps_to_cmd", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\webdriver_manager\\core\\os_manager.py"}