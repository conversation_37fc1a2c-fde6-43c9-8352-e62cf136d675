{"data_mtime": 1757882372, "dep_lines": [20, 21, 22, 23, 17, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30], "dependencies": ["selenium.webdriver.common.desired_capabilities", "selenium.webdriver.common.options", "selenium.webdriver.firefox.firefox_binary", "selenium.webdriver.firefox.firefox_profile", "typing", "builtins", "_collections_abc", "_frozen_importlib", "abc", "selenium.webdriver.common", "types"], "hash": "8482e90a4e6a29ffa85179cb56f0b9314d7f6e03", "id": "selenium.webdriver.firefox.options", "ignore_all": true, "interface_hash": "776af5e11b409ff20881ba7a4a7ac6512619b2e2", "mtime": 1757882171, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\selenium\\webdriver\\firefox\\options.py", "plugin_data": null, "size": 4417, "suppressed": [], "version_id": "1.15.0"}