{"data_mtime": 1757882370, "dep_lines": [18, 46, 1, 1, 1, 1, 1], "dep_prios": [5, 20, 5, 30, 30, 30, 30], "dependencies": ["selenium.webdriver.remote.command", "weakref", "builtins", "_frozen_importlib", "_weakref", "abc", "typing"], "hash": "9d721c018daca4a7e427dd70315828f0328d0d12", "id": "selenium.webdriver.remote.mobile", "ignore_all": true, "interface_hash": "5508d0db143b832fc12ac52d6dafebb224e65777", "mtime": 1757882171, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\selenium\\webdriver\\remote\\mobile.py", "plugin_data": null, "size": 2616, "suppressed": [], "version_id": "1.15.0"}