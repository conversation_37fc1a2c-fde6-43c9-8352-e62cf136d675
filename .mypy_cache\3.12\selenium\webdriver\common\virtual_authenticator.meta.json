{"data_mtime": 1757882369, "dep_lines": [18, 19, 20, 22, 1, 1, 1, 1], "dep_prios": [10, 10, 5, 5, 5, 30, 30, 30], "dependencies": ["functools", "typing", "base64", "enum", "builtins", "_collections_abc", "_frozen_importlib", "abc"], "hash": "2b5f904162658a7f94095d641dc6fef56e60b8f3", "id": "selenium.webdriver.common.virtual_authenticator", "ignore_all": true, "interface_hash": "ab87c35d3c8f73022b5d08f93d50e10f46e30b0a", "mtime": 1757882171, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\selenium\\webdriver\\common\\virtual_authenticator.py", "plugin_data": null, "size": 7827, "suppressed": [], "version_id": "1.15.0"}