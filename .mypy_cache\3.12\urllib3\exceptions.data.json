{".class": "MypyFile", "_fullname": "urllib3.exceptions", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "BodyNotHttplibCompatible": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["urllib3.exceptions.HTTPError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "urllib3.exceptions.BodyNotHttplibCompatible", "name": "BodyNotHttplibCompatible", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "urllib3.exceptions.BodyNotHttplibCompatible", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "urllib3.exceptions", "mro": ["urllib3.exceptions.BodyNotHttplibCompatible", "urllib3.exceptions.HTTPError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "urllib3.exceptions.BodyNotHttplibCompatible.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "urllib3.exceptions.BodyNotHttplibCompatible", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ClosedPoolError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["urllib3.exceptions.PoolError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "urllib3.exceptions.ClosedPoolError", "name": "ClosedPoolError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "urllib3.exceptions.ClosedPoolError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "urllib3.exceptions", "mro": ["urllib3.exceptions.ClosedPoolError", "urllib3.exceptions.PoolError", "urllib3.exceptions.HTTPError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "urllib3.exceptions.ClosedPoolError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "urllib3.exceptions.ClosedPoolError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ConnectTimeoutError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["urllib3.exceptions.TimeoutError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "urllib3.exceptions.ConnectTimeoutError", "name": "ConnectTimeoutError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "urllib3.exceptions.ConnectTimeoutError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "urllib3.exceptions", "mro": ["urllib3.exceptions.ConnectTimeoutError", "urllib3.exceptions.TimeoutError", "urllib3.exceptions.HTTPError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "urllib3.exceptions.ConnectTimeoutError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "urllib3.exceptions.ConnectTimeoutError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ConnectionError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "urllib3.exceptions.ConnectionError", "line": 78, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "urllib3.exceptions.ProtocolError"}}, "ConnectionPool": {".class": "SymbolTableNode", "cross_ref": "urllib3.connectionpool.ConnectionPool", "kind": "Gdef"}, "DecodeError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["urllib3.exceptions.HTTPError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "urllib3.exceptions.DecodeError", "name": "DecodeError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "urllib3.exceptions.DecodeError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "urllib3.exceptions", "mro": ["urllib3.exceptions.DecodeError", "urllib3.exceptions.HTTPError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "urllib3.exceptions.DecodeError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "urllib3.exceptions.DecodeError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DependencyWarning": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["urllib3.exceptions.HTTPWarning"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "urllib3.exceptions.DependencyWarning", "name": "DependencyWarning", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "urllib3.exceptions.DependencyWarning", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "urllib3.exceptions", "mro": ["urllib3.exceptions.DependencyWarning", "urllib3.exceptions.HTTPWarning", "builtins.Warning", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "urllib3.exceptions.DependencyWarning.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "urllib3.exceptions.DependencyWarning", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "EmptyPoolError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["urllib3.exceptions.PoolError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "urllib3.exceptions.EmptyPoolError", "name": "EmptyPoolError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "urllib3.exceptions.EmptyPoolError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "urllib3.exceptions", "mro": ["urllib3.exceptions.EmptyPoolError", "urllib3.exceptions.PoolError", "urllib3.exceptions.HTTPError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "urllib3.exceptions.EmptyPoolError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "urllib3.exceptions.EmptyPoolError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FullPoolError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["urllib3.exceptions.PoolError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "urllib3.exceptions.FullPoolError", "name": "FullPoolError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "urllib3.exceptions.FullPoolError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "urllib3.exceptions", "mro": ["urllib3.exceptions.FullPoolError", "urllib3.exceptions.PoolError", "urllib3.exceptions.HTTPError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "urllib3.exceptions.FullPoolError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "urllib3.exceptions.FullPoolError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HTTPConnection": {".class": "SymbolTableNode", "cross_ref": "urllib3.connection.HTTPConnection", "kind": "Gdef"}, "HTTPError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.Exception"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "urllib3.exceptions.HTTPError", "name": "HTTPError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "urllib3.exceptions.HTTPError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "urllib3.exceptions", "mro": ["urllib3.exceptions.HTTPError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "urllib3.exceptions.HTTPError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "urllib3.exceptions.HTTPError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HTTPResponse": {".class": "SymbolTableNode", "cross_ref": "urllib3.response.HTTPResponse", "kind": "Gdef"}, "HTTPWarning": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.Warning"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "urllib3.exceptions.HTTPWarning", "name": "HTTPWarning", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "urllib3.exceptions.HTTPWarning", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "urllib3.exceptions", "mro": ["urllib3.exceptions.HTTPWarning", "builtins.Warning", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "urllib3.exceptions.HTTPWarning.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "urllib3.exceptions.HTTPWarning", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HeaderParsingError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["urllib3.exceptions.HTTPError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "urllib3.exceptions.HeaderParsingError", "name": "HeaderParsingError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "urllib3.exceptions.HeaderParsingError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "urllib3.exceptions", "mro": ["urllib3.exceptions.HeaderParsingError", "urllib3.exceptions.HTTPError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "defects", "unparsed_data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "urllib3.exceptions.HeaderParsingError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "defects", "unparsed_data"], "arg_types": ["urllib3.exceptions.HeaderParsingError", {".class": "Instance", "args": ["email.errors.MessageDefect"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["builtins.bytes", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of HeaderParsingError", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "urllib3.exceptions.HeaderParsingError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "urllib3.exceptions.HeaderParsingError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HostChangedError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["urllib3.exceptions.RequestError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "urllib3.exceptions.HostChangedError", "name": "HostChangedError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "urllib3.exceptions.HostChangedError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "urllib3.exceptions", "mro": ["urllib3.exceptions.HostChangedError", "urllib3.exceptions.RequestError", "urllib3.exceptions.PoolError", "urllib3.exceptions.HTTPError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "pool", "url", "retries"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "urllib3.exceptions.HostChangedError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "pool", "url", "retries"], "arg_types": ["urllib3.exceptions.HostChangedError", "urllib3.connectionpool.ConnectionPool", "builtins.str", {".class": "UnionType", "items": ["urllib3.util.retry.Retry", "builtins.int"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of HostChangedError", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "retries": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "urllib3.exceptions.HostChangedError.retries", "name": "retries", "type": {".class": "UnionType", "items": ["urllib3.util.retry.Retry", "builtins.int"], "uses_pep604_syntax": false}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "urllib3.exceptions.HostChangedError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "urllib3.exceptions.HostChangedError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "IncompleteRead": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["urllib3.exceptions.HTTPError", "http.client.IncompleteRead"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "urllib3.exceptions.IncompleteRead", "name": "IncompleteRead", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "urllib3.exceptions.IncompleteRead", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "urllib3.exceptions", "mro": ["urllib3.exceptions.IncompleteRead", "urllib3.exceptions.HTTPError", "http.client.IncompleteRead", "http.client.HTTPException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "partial", "expected"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "urllib3.exceptions.IncompleteRead.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "partial", "expected"], "arg_types": ["urllib3.exceptions.IncompleteRead", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of IncompleteRead", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "urllib3.exceptions.IncompleteRead.__repr__", "name": "__repr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["urllib3.exceptions.IncompleteRead"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__repr__ of IncompleteRead", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "expected": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "urllib3.exceptions.IncompleteRead.expected", "name": "expected", "type": "builtins.int"}}, "partial": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "urllib3.exceptions.IncompleteRead.partial", "name": "partial", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "urllib3.exceptions.IncompleteRead.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "urllib3.exceptions.IncompleteRead", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InsecurePlatformWarning": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["urllib3.exceptions.SecurityWarning"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "urllib3.exceptions.InsecurePlatformWarning", "name": "InsecurePlatformWarning", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "urllib3.exceptions.InsecurePlatformWarning", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "urllib3.exceptions", "mro": ["urllib3.exceptions.InsecurePlatformWarning", "urllib3.exceptions.SecurityWarning", "urllib3.exceptions.HTTPWarning", "builtins.Warning", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "urllib3.exceptions.InsecurePlatformWarning.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "urllib3.exceptions.InsecurePlatformWarning", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InsecureRequestWarning": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["urllib3.exceptions.SecurityWarning"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "urllib3.exceptions.InsecureRequestWarning", "name": "InsecureRequestWarning", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "urllib3.exceptions.InsecureRequestWarning", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "urllib3.exceptions", "mro": ["urllib3.exceptions.InsecureRequestWarning", "urllib3.exceptions.SecurityWarning", "urllib3.exceptions.HTTPWarning", "builtins.Warning", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "urllib3.exceptions.InsecureRequestWarning.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "urllib3.exceptions.InsecureRequestWarning", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InvalidChunkLength": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["urllib3.exceptions.HTTPError", "http.client.IncompleteRead"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "urllib3.exceptions.InvalidChunkLength", "name": "InvalidChunkLength", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "urllib3.exceptions.InvalidChunkLength", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "urllib3.exceptions", "mro": ["urllib3.exceptions.InvalidChunkLength", "urllib3.exceptions.HTTPError", "http.client.IncompleteRead", "http.client.HTTPException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "response", "length"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "urllib3.exceptions.InvalidChunkLength.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "response", "length"], "arg_types": ["urllib3.exceptions.InvalidChunkLength", "urllib3.response.HTTPResponse", "builtins.bytes"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of InvalidChunkLength", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "urllib3.exceptions.InvalidChunkLength.__repr__", "name": "__repr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["urllib3.exceptions.InvalidChunkLength"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__repr__ of InvalidChunkLength", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "expected": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "urllib3.exceptions.InvalidChunkLength.expected", "name": "expected", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "length": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "urllib3.exceptions.InvalidChunkLength.length", "name": "length", "type": "builtins.bytes"}}, "partial": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "urllib3.exceptions.InvalidChunkLength.partial", "name": "partial", "type": "builtins.int"}}, "response": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "urllib3.exceptions.InvalidChunkLength.response", "name": "response", "type": "urllib3.response.HTTPResponse"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "urllib3.exceptions.InvalidChunkLength.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "urllib3.exceptions.InvalidChunkLength", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InvalidHeader": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["urllib3.exceptions.HTTPError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "urllib3.exceptions.InvalidHeader", "name": "InvalidHeader", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "urllib3.exceptions.InvalidHeader", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "urllib3.exceptions", "mro": ["urllib3.exceptions.InvalidHeader", "urllib3.exceptions.HTTPError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "urllib3.exceptions.InvalidHeader.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "urllib3.exceptions.InvalidHeader", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "LocationParseError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["urllib3.exceptions.LocationValueError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "urllib3.exceptions.LocationParseError", "name": "LocationParseError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "urllib3.exceptions.LocationParseError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "urllib3.exceptions", "mro": ["urllib3.exceptions.LocationParseError", "urllib3.exceptions.LocationValueError", "builtins.ValueError", "urllib3.exceptions.HTTPError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "location"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "urllib3.exceptions.LocationParseError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "location"], "arg_types": ["urllib3.exceptions.LocationParseError", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of LocationParseError", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "location": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "urllib3.exceptions.LocationParseError.location", "name": "location", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "urllib3.exceptions.LocationParseError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "urllib3.exceptions.LocationParseError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "LocationValueError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.ValueError", "urllib3.exceptions.HTTPError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "urllib3.exceptions.LocationValueError", "name": "LocationValueError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "urllib3.exceptions.LocationValueError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "urllib3.exceptions", "mro": ["urllib3.exceptions.LocationValueError", "builtins.ValueError", "urllib3.exceptions.HTTPError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "urllib3.exceptions.LocationValueError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "urllib3.exceptions.LocationValueError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MaxRetryError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["urllib3.exceptions.RequestError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "urllib3.exceptions.MaxRetryError", "name": "MaxRetryError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "urllib3.exceptions.MaxRetryError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "urllib3.exceptions", "mro": ["urllib3.exceptions.MaxRetryError", "urllib3.exceptions.RequestError", "urllib3.exceptions.PoolError", "urllib3.exceptions.HTTPError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "pool", "url", "reason"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "urllib3.exceptions.MaxRetryError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "pool", "url", "reason"], "arg_types": ["urllib3.exceptions.MaxRetryError", "urllib3.connectionpool.ConnectionPool", "builtins.str", {".class": "UnionType", "items": ["builtins.Exception", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of MaxRetryError", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__reduce__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "urllib3.exceptions.MaxRetryError.__reduce__", "name": "__reduce__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["urllib3.exceptions.MaxRetryError"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__reduce__ of MaxRetryError", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "urllib3.exceptions._TYPE_REDUCE_RESULT"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "reason": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "urllib3.exceptions.MaxRetryError.reason", "name": "reason", "type": {".class": "UnionType", "items": ["builtins.Exception", {".class": "NoneType"}], "uses_pep604_syntax": true}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "urllib3.exceptions.MaxRetryError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "urllib3.exceptions.MaxRetryError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MessageDefect": {".class": "SymbolTableNode", "cross_ref": "email.errors.MessageDefect", "kind": "Gdef"}, "NameResolutionError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["urllib3.exceptions.NewConnectionError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "urllib3.exceptions.NameResolutionError", "name": "NameResolutionError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "urllib3.exceptions.NameResolutionError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "urllib3.exceptions", "mro": ["urllib3.exceptions.NameResolutionError", "urllib3.exceptions.NewConnectionError", "urllib3.exceptions.ConnectTimeoutError", "urllib3.exceptions.TimeoutError", "urllib3.exceptions.HTTPError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "host", "conn", "reason"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "urllib3.exceptions.NameResolutionError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "host", "conn", "reason"], "arg_types": ["urllib3.exceptions.NameResolutionError", "builtins.str", "urllib3.connection.HTTPConnection", "socket.gaierror"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of NameResolutionError", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__reduce__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "urllib3.exceptions.NameResolutionError.__reduce__", "name": "__reduce__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["urllib3.exceptions.NameResolutionError"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__reduce__ of NameResolutionError", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "urllib3.exceptions._TYPE_REDUCE_RESULT"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_host": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "urllib3.exceptions.NameResolutionError._host", "name": "_host", "type": "builtins.str"}}, "_reason": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "urllib3.exceptions.NameResolutionError._reason", "name": "_reason", "type": "socket.gaierror"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "urllib3.exceptions.NameResolutionError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "urllib3.exceptions.NameResolutionError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NewConnectionError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["urllib3.exceptions.ConnectTimeoutError", "urllib3.exceptions.HTTPError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "urllib3.exceptions.NewConnectionError", "name": "NewConnectionError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "urllib3.exceptions.NewConnectionError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "urllib3.exceptions", "mro": ["urllib3.exceptions.NewConnectionError", "urllib3.exceptions.ConnectTimeoutError", "urllib3.exceptions.TimeoutError", "urllib3.exceptions.HTTPError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "conn", "message"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "urllib3.exceptions.NewConnectionError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "conn", "message"], "arg_types": ["urllib3.exceptions.NewConnectionError", "urllib3.connection.HTTPConnection", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of NewConnectionError", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__reduce__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "urllib3.exceptions.NewConnectionError.__reduce__", "name": "__reduce__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["urllib3.exceptions.NewConnectionError"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__reduce__ of NewConnectionError", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "urllib3.exceptions._TYPE_REDUCE_RESULT"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_message": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "urllib3.exceptions.NewConnectionError._message", "name": "_message", "type": "builtins.str"}}, "conn": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "urllib3.exceptions.NewConnectionError.conn", "name": "conn", "type": "urllib3.connection.HTTPConnection"}}, "pool": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "urllib3.exceptions.NewConnectionError.pool", "name": "pool", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["urllib3.exceptions.NewConnectionError"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pool of NewConnectionError", "ret_type": "urllib3.connection.HTTPConnection", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "urllib3.exceptions.NewConnectionError.pool", "name": "pool", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["urllib3.exceptions.NewConnectionError"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pool of NewConnectionError", "ret_type": "urllib3.connection.HTTPConnection", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "urllib3.exceptions.NewConnectionError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "urllib3.exceptions.NewConnectionError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NotOpenSSLWarning": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["urllib3.exceptions.SecurityWarning"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "urllib3.exceptions.NotOpenSSLWarning", "name": "NotOpenSSLWarning", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "urllib3.exceptions.NotOpenSSLWarning", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "urllib3.exceptions", "mro": ["urllib3.exceptions.NotOpenSSLWarning", "urllib3.exceptions.SecurityWarning", "urllib3.exceptions.HTTPWarning", "builtins.Warning", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "urllib3.exceptions.NotOpenSSLWarning.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "urllib3.exceptions.NotOpenSSLWarning", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PoolError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["urllib3.exceptions.HTTPError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "urllib3.exceptions.PoolError", "name": "PoolError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "urllib3.exceptions.PoolError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "urllib3.exceptions", "mro": ["urllib3.exceptions.PoolError", "urllib3.exceptions.HTTPError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "pool", "message"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "urllib3.exceptions.PoolError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "pool", "message"], "arg_types": ["urllib3.exceptions.PoolError", "urllib3.connectionpool.ConnectionPool", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of PoolError", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__reduce__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "urllib3.exceptions.PoolError.__reduce__", "name": "__reduce__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["urllib3.exceptions.PoolError"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__reduce__ of PoolError", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "urllib3.exceptions._TYPE_REDUCE_RESULT"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_message": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "urllib3.exceptions.PoolError._message", "name": "_message", "type": "builtins.str"}}, "pool": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "urllib3.exceptions.PoolError.pool", "name": "pool", "type": "urllib3.connectionpool.ConnectionPool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "urllib3.exceptions.PoolError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "urllib3.exceptions.PoolError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ProtocolError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["urllib3.exceptions.HTTPError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "urllib3.exceptions.ProtocolError", "name": "ProtocolError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "urllib3.exceptions.ProtocolError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "urllib3.exceptions", "mro": ["urllib3.exceptions.ProtocolError", "urllib3.exceptions.HTTPError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "urllib3.exceptions.ProtocolError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "urllib3.exceptions.ProtocolError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ProxyError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["urllib3.exceptions.HTTPError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "urllib3.exceptions.ProxyError", "name": "ProxyError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "urllib3.exceptions.ProxyError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "urllib3.exceptions", "mro": ["urllib3.exceptions.ProxyError", "urllib3.exceptions.HTTPError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "message", "error"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "urllib3.exceptions.ProxyError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "message", "error"], "arg_types": ["urllib3.exceptions.ProxyError", "builtins.str", "builtins.Exception"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ProxyError", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "original_error": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "urllib3.exceptions.ProxyError.original_error", "name": "original_error", "type": "builtins.Exception"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "urllib3.exceptions.ProxyError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "urllib3.exceptions.ProxyError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ProxySchemeUnknown": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.AssertionError", "urllib3.exceptions.URLSchemeUnknown"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "urllib3.exceptions.ProxySchemeUnknown", "name": "ProxySchemeUnknown", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "urllib3.exceptions.ProxySchemeUnknown", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "urllib3.exceptions", "mro": ["urllib3.exceptions.ProxySchemeUnknown", "builtins.AssertionError", "urllib3.exceptions.URLSchemeUnknown", "urllib3.exceptions.LocationValueError", "builtins.ValueError", "urllib3.exceptions.HTTPError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "scheme"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "urllib3.exceptions.ProxySchemeUnknown.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "scheme"], "arg_types": ["urllib3.exceptions.ProxySchemeUnknown", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ProxySchemeUnknown", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "urllib3.exceptions.ProxySchemeUnknown.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "urllib3.exceptions.ProxySchemeUnknown", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ProxySchemeUnsupported": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.ValueError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "urllib3.exceptions.ProxySchemeUnsupported", "name": "ProxySchemeUnsupported", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "urllib3.exceptions.ProxySchemeUnsupported", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "urllib3.exceptions", "mro": ["urllib3.exceptions.ProxySchemeUnsupported", "builtins.ValueError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "urllib3.exceptions.ProxySchemeUnsupported.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "urllib3.exceptions.ProxySchemeUnsupported", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ReadTimeoutError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["urllib3.exceptions.TimeoutError", "urllib3.exceptions.RequestError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "urllib3.exceptions.ReadTimeoutError", "name": "ReadTimeoutError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "urllib3.exceptions.ReadTimeoutError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "urllib3.exceptions", "mro": ["urllib3.exceptions.ReadTimeoutError", "urllib3.exceptions.TimeoutError", "urllib3.exceptions.RequestError", "urllib3.exceptions.PoolError", "urllib3.exceptions.HTTPError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "urllib3.exceptions.ReadTimeoutError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "urllib3.exceptions.ReadTimeoutError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "RequestError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["urllib3.exceptions.PoolError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "urllib3.exceptions.RequestError", "name": "RequestError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "urllib3.exceptions.RequestError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "urllib3.exceptions", "mro": ["urllib3.exceptions.RequestError", "urllib3.exceptions.PoolError", "urllib3.exceptions.HTTPError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "pool", "url", "message"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "urllib3.exceptions.RequestError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "pool", "url", "message"], "arg_types": ["urllib3.exceptions.RequestError", "urllib3.connectionpool.ConnectionPool", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of RequestError", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__reduce__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "urllib3.exceptions.RequestError.__reduce__", "name": "__reduce__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["urllib3.exceptions.RequestError"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__reduce__ of RequestError", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "urllib3.exceptions._TYPE_REDUCE_RESULT"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "url": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "urllib3.exceptions.RequestError.url", "name": "url", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "urllib3.exceptions.RequestError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "urllib3.exceptions.RequestError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ResponseError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["urllib3.exceptions.HTTPError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "urllib3.exceptions.ResponseError", "name": "ResponseError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "urllib3.exceptions.ResponseError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "urllib3.exceptions", "mro": ["urllib3.exceptions.ResponseError", "urllib3.exceptions.HTTPError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "GENERIC_ERROR": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "urllib3.exceptions.ResponseError.GENERIC_ERROR", "name": "GENERIC_ERROR", "type": "builtins.str"}}, "SPECIFIC_ERROR": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "urllib3.exceptions.ResponseError.SPECIFIC_ERROR", "name": "SPECIFIC_ERROR", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "urllib3.exceptions.ResponseError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "urllib3.exceptions.ResponseError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ResponseNotChunked": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["urllib3.exceptions.ProtocolError", "builtins.ValueError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "urllib3.exceptions.ResponseNotChunked", "name": "ResponseNotChunked", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "urllib3.exceptions.ResponseNotChunked", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "urllib3.exceptions", "mro": ["urllib3.exceptions.ResponseNotChunked", "urllib3.exceptions.ProtocolError", "urllib3.exceptions.HTTPError", "builtins.ValueError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "urllib3.exceptions.ResponseNotChunked.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "urllib3.exceptions.ResponseNotChunked", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Retry": {".class": "SymbolTableNode", "cross_ref": "urllib3.util.retry.Retry", "kind": "Gdef"}, "SSLError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["urllib3.exceptions.HTTPError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "urllib3.exceptions.SSLError", "name": "SSLError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "urllib3.exceptions.SSLError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "urllib3.exceptions", "mro": ["urllib3.exceptions.SSLError", "urllib3.exceptions.HTTPError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "urllib3.exceptions.SSLError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "urllib3.exceptions.SSLError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SecurityWarning": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["urllib3.exceptions.HTTPWarning"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "urllib3.exceptions.SecurityWarning", "name": "SecurityWarning", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "urllib3.exceptions.SecurityWarning", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "urllib3.exceptions", "mro": ["urllib3.exceptions.SecurityWarning", "urllib3.exceptions.HTTPWarning", "builtins.Warning", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "urllib3.exceptions.SecurityWarning.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "urllib3.exceptions.SecurityWarning", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SystemTimeWarning": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["urllib3.exceptions.SecurityWarning"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "urllib3.exceptions.SystemTimeWarning", "name": "SystemTimeWarning", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "urllib3.exceptions.SystemTimeWarning", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "urllib3.exceptions", "mro": ["urllib3.exceptions.SystemTimeWarning", "urllib3.exceptions.SecurityWarning", "urllib3.exceptions.HTTPWarning", "builtins.Warning", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "urllib3.exceptions.SystemTimeWarning.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "urllib3.exceptions.SystemTimeWarning", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TimeoutError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["urllib3.exceptions.HTTPError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "urllib3.exceptions.TimeoutError", "name": "TimeoutError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "urllib3.exceptions.TimeoutError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "urllib3.exceptions", "mro": ["urllib3.exceptions.TimeoutError", "urllib3.exceptions.HTTPError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "urllib3.exceptions.TimeoutError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "urllib3.exceptions.TimeoutError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TimeoutStateError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["urllib3.exceptions.HTTPError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "urllib3.exceptions.TimeoutStateError", "name": "TimeoutStateError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "urllib3.exceptions.TimeoutStateError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "urllib3.exceptions", "mro": ["urllib3.exceptions.TimeoutStateError", "urllib3.exceptions.HTTPError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "urllib3.exceptions.TimeoutStateError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "urllib3.exceptions.TimeoutStateError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "URLSchemeUnknown": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["urllib3.exceptions.LocationValueError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "urllib3.exceptions.URLSchemeUnknown", "name": "URLSchemeUnknown", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "urllib3.exceptions.URLSchemeUnknown", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "urllib3.exceptions", "mro": ["urllib3.exceptions.URLSchemeUnknown", "urllib3.exceptions.LocationValueError", "builtins.ValueError", "urllib3.exceptions.HTTPError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "scheme"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "urllib3.exceptions.URLSchemeUnknown.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "scheme"], "arg_types": ["urllib3.exceptions.URLSchemeUnknown", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of URLSchemeUnknown", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "scheme": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "urllib3.exceptions.URLSchemeUnknown.scheme", "name": "scheme", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "urllib3.exceptions.URLSchemeUnknown.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "urllib3.exceptions.URLSchemeUnknown", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "UnrewindableBodyError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["urllib3.exceptions.HTTPError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "urllib3.exceptions.UnrewindableBodyError", "name": "UnrewindableBodyError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "urllib3.exceptions.UnrewindableBodyError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "urllib3.exceptions", "mro": ["urllib3.exceptions.UnrewindableBodyError", "urllib3.exceptions.HTTPError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "urllib3.exceptions.UnrewindableBodyError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "urllib3.exceptions.UnrewindableBodyError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_TYPE_REDUCE_RESULT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "urllib3.exceptions._TYPE_REDUCE_RESULT", "line": 26, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "TupleType", "implicit": false, "items": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": "builtins.object", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.tuple"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "urllib3.exceptions.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "urllib3.exceptions.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "urllib3.exceptions.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "urllib3.exceptions.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "urllib3.exceptions.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "urllib3.exceptions.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "httplib_IncompleteRead": {".class": "SymbolTableNode", "cross_ref": "http.client.IncompleteRead", "kind": "Gdef"}, "socket": {".class": "SymbolTableNode", "cross_ref": "socket", "kind": "Gdef"}, "typing": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef"}, "warnings": {".class": "SymbolTableNode", "cross_ref": "warnings", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\urllib3\\exceptions.py"}