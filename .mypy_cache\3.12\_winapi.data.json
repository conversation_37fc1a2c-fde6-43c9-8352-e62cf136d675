{".class": "MypyFile", "_fullname": "_winapi", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "ABOVE_NORMAL_PRIORITY_CLASS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 32768, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "_winapi.ABOVE_NORMAL_PRIORITY_CLASS", "name": "ABOVE_NORMAL_PRIORITY_CLASS", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 32768}, "type_ref": "builtins.int"}}}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "BELOW_NORMAL_PRIORITY_CLASS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 16384, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "_winapi.BELOW_NORMAL_PRIORITY_CLASS", "name": "BELOW_NORMAL_PRIORITY_CLASS", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 16384}, "type_ref": "builtins.int"}}}, "COPYFILE2_CALLBACK_CHUNK_FINISHED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 2, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "_winapi.COPYFILE2_CALLBACK_CHUNK_FINISHED", "name": "COPYFILE2_CALLBACK_CHUNK_FINISHED", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 2}, "type_ref": "builtins.int"}}}, "COPYFILE2_CALLBACK_CHUNK_STARTED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 1, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "_winapi.COPYFILE2_CALLBACK_CHUNK_STARTED", "name": "COPYFILE2_CALLBACK_CHUNK_STARTED", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 1}, "type_ref": "builtins.int"}}}, "COPYFILE2_CALLBACK_ERROR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 6, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "_winapi.COPYFILE2_CALLBACK_ERROR", "name": "COPYFILE2_CALLBACK_ERROR", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 6}, "type_ref": "builtins.int"}}}, "COPYFILE2_CALLBACK_POLL_CONTINUE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 5, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "_winapi.COPYFILE2_CALLBACK_POLL_CONTINUE", "name": "COPYFILE2_CALLBACK_POLL_CONTINUE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 5}, "type_ref": "builtins.int"}}}, "COPYFILE2_CALLBACK_STREAM_FINISHED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 4, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "_winapi.COPYFILE2_CALLBACK_STREAM_FINISHED", "name": "COPYFILE2_CALLBACK_STREAM_FINISHED", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 4}, "type_ref": "builtins.int"}}}, "COPYFILE2_CALLBACK_STREAM_STARTED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 3, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "_winapi.COPYFILE2_CALLBACK_STREAM_STARTED", "name": "COPYFILE2_CALLBACK_STREAM_STARTED", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 3}, "type_ref": "builtins.int"}}}, "COPYFILE2_PROGRESS_CANCEL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 1, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "_winapi.COPYFILE2_PROGRESS_CANCEL", "name": "COPYFILE2_PROGRESS_CANCEL", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 1}, "type_ref": "builtins.int"}}}, "COPYFILE2_PROGRESS_CONTINUE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 0, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "_winapi.COPYFILE2_PROGRESS_CONTINUE", "name": "COPYFILE2_PROGRESS_CONTINUE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 0}, "type_ref": "builtins.int"}}}, "COPYFILE2_PROGRESS_PAUSE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 4, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "_winapi.COPYFILE2_PROGRESS_PAUSE", "name": "COPYFILE2_PROGRESS_PAUSE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 4}, "type_ref": "builtins.int"}}}, "COPYFILE2_PROGRESS_QUIET": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 3, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "_winapi.COPYFILE2_PROGRESS_QUIET", "name": "COPYFILE2_PROGRESS_QUIET", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 3}, "type_ref": "builtins.int"}}}, "COPYFILE2_PROGRESS_STOP": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 2, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "_winapi.COPYFILE2_PROGRESS_STOP", "name": "COPYFILE2_PROGRESS_STOP", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 2}, "type_ref": "builtins.int"}}}, "COPY_FILE_ALLOW_DECRYPTED_DESTINATION": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 8, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "_winapi.COPY_FILE_ALLOW_DECRYPTED_DESTINATION", "name": "COPY_FILE_ALLOW_DECRYPTED_DESTINATION", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 8}, "type_ref": "builtins.int"}}}, "COPY_FILE_COPY_SYMLINK": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 2048, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "_winapi.COPY_FILE_COPY_SYMLINK", "name": "COPY_FILE_COPY_SYMLINK", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 2048}, "type_ref": "builtins.int"}}}, "COPY_FILE_FAIL_IF_EXISTS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 1, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "_winapi.COPY_FILE_FAIL_IF_EXISTS", "name": "COPY_FILE_FAIL_IF_EXISTS", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 1}, "type_ref": "builtins.int"}}}, "COPY_FILE_NO_BUFFERING": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 4096, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "_winapi.COPY_FILE_NO_BUFFERING", "name": "COPY_FILE_NO_BUFFERING", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 4096}, "type_ref": "builtins.int"}}}, "COPY_FILE_NO_OFFLOAD": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 262144, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "_winapi.COPY_FILE_NO_OFFLOAD", "name": "COPY_FILE_NO_OFFLOAD", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 262144}, "type_ref": "builtins.int"}}}, "COPY_FILE_OPEN_SOURCE_FOR_WRITE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 4, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "_winapi.COPY_FILE_OPEN_SOURCE_FOR_WRITE", "name": "COPY_FILE_O<PERSON>EN_SOURCE_FOR_WRITE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 4}, "type_ref": "builtins.int"}}}, "COPY_FILE_REQUEST_COMPRESSED_TRAFFIC": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 268435456, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "_winapi.COPY_FILE_REQUEST_COMPRESSED_TRAFFIC", "name": "COPY_FILE_REQUEST_COMPRESSED_TRAFFIC", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 268435456}, "type_ref": "builtins.int"}}}, "COPY_FILE_REQUEST_SECURITY_PRIVILEGES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 8192, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "_winapi.COPY_FILE_REQUEST_SECURITY_PRIVILEGES", "name": "COPY_FILE_REQUEST_SECURITY_PRIVILEGES", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 8192}, "type_ref": "builtins.int"}}}, "COPY_FILE_RESTARTABLE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 2, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "_winapi.COPY_FILE_RESTARTABLE", "name": "COPY_FILE_RESTARTABLE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 2}, "type_ref": "builtins.int"}}}, "COPY_FILE_RESUME_FROM_PAUSE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 16384, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "_winapi.COPY_FILE_RESUME_FROM_PAUSE", "name": "COPY_FILE_RESUME_FROM_PAUSE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 16384}, "type_ref": "builtins.int"}}}, "CREATE_BREAKAWAY_FROM_JOB": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 16777216, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "_winapi.CREATE_BREAKAWAY_FROM_JOB", "name": "CREATE_BREAKAWAY_FROM_JOB", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 16777216}, "type_ref": "builtins.int"}}}, "CREATE_DEFAULT_ERROR_MODE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 67108864, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "_winapi.CREATE_DEFAULT_ERROR_MODE", "name": "CREATE_DEFAULT_ERROR_MODE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 67108864}, "type_ref": "builtins.int"}}}, "CREATE_NEW_CONSOLE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 16, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "_winapi.CREATE_NEW_CONSOLE", "name": "CREATE_NEW_CONSOLE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 16}, "type_ref": "builtins.int"}}}, "CREATE_NEW_PROCESS_GROUP": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 512, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "_winapi.CREATE_NEW_PROCESS_GROUP", "name": "CREATE_NEW_PROCESS_GROUP", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 512}, "type_ref": "builtins.int"}}}, "CREATE_NO_WINDOW": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": *********, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "_winapi.CREATE_NO_WINDOW", "name": "CREATE_NO_WINDOW", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": *********}, "type_ref": "builtins.int"}}}, "CloseHandle": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_winapi.CloseHandle", "name": "CloseHandle", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "CloseHandle", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ConnectNamedPipe": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "_winapi.ConnectNamedPipe", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["handle", "overlapped"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "_winapi.ConnectNamedPipe", "name": "ConnectNamedPipe", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["handle", "overlapped"], "arg_types": ["builtins.int", {".class": "LiteralType", "fallback": "builtins.bool", "value": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ConnectNamedPipe", "ret_type": "_winapi.Overlapped", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "_winapi.ConnectNamedPipe", "name": "ConnectNamedPipe", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["handle", "overlapped"], "arg_types": ["builtins.int", {".class": "LiteralType", "fallback": "builtins.bool", "value": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ConnectNamedPipe", "ret_type": "_winapi.Overlapped", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["handle", "overlapped"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "_winapi.ConnectNamedPipe", "name": "ConnectNamedPipe", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["handle", "overlapped"], "arg_types": ["builtins.int", {".class": "LiteralType", "fallback": "builtins.bool", "value": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ConnectNamedPipe", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "_winapi.ConnectNamedPipe", "name": "ConnectNamedPipe", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["handle", "overlapped"], "arg_types": ["builtins.int", {".class": "LiteralType", "fallback": "builtins.bool", "value": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ConnectNamedPipe", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["handle", "overlapped"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "_winapi.ConnectNamedPipe", "name": "ConnectNamedPipe", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["handle", "overlapped"], "arg_types": ["builtins.int", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ConnectNamedPipe", "ret_type": {".class": "UnionType", "items": ["_winapi.Overlapped", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "_winapi.ConnectNamedPipe", "name": "ConnectNamedPipe", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["handle", "overlapped"], "arg_types": ["builtins.int", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ConnectNamedPipe", "ret_type": {".class": "UnionType", "items": ["_winapi.Overlapped", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["handle", "overlapped"], "arg_types": ["builtins.int", {".class": "LiteralType", "fallback": "builtins.bool", "value": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ConnectNamedPipe", "ret_type": "_winapi.Overlapped", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["handle", "overlapped"], "arg_types": ["builtins.int", {".class": "LiteralType", "fallback": "builtins.bool", "value": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ConnectNamedPipe", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["handle", "overlapped"], "arg_types": ["builtins.int", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ConnectNamedPipe", "ret_type": {".class": "UnionType", "items": ["_winapi.Overlapped", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "CopyFile2": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["existing_file_name", "new_file_name", "flags", "progress_routine"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_winapi.CopyFile2", "name": "CopyFile2", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["existing_file_name", "new_file_name", "flags", "progress_routine"], "arg_types": ["builtins.str", "builtins.str", "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "CopyFile2", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "CreateFile": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": [null, null, null, null, null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_winapi.CreateFile", "name": "CreateFile", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": [null, null, null, null, null, null, null], "arg_types": ["builtins.str", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "CreateFile", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "CreateJunction": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_winapi.CreateJunction", "name": "CreateJunction", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "CreateJunction", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "CreateNamedPipe": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0], "arg_names": [null, null, null, null, null, null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_winapi.CreateNamedPipe", "name": "CreateNamedPipe", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0], "arg_names": [null, null, null, null, null, null, null, null], "arg_types": ["builtins.str", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "CreateNamedPipe", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "CreatePipe": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_winapi.CreatePipe", "name": "CreatePipe", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "CreatePipe", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "CreateProcess": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": [null, null, null, null, null, null, null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_winapi.CreateProcess", "name": "CreateProcess", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": [null, null, null, null, null, null, null, null, null], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.bool", "builtins.int", {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "CreateProcess", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int", "builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "DETACHED_PROCESS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 8, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "_winapi.DETACHED_PROCESS", "name": "DETACHED_PROCESS", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 8}, "type_ref": "builtins.int"}}}, "DUPLICATE_CLOSE_SOURCE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 1, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "_winapi.DUPLICATE_CLOSE_SOURCE", "name": "DUPLICATE_CLOSE_SOURCE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 1}, "type_ref": "builtins.int"}}}, "DUPLICATE_SAME_ACCESS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 2, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "_winapi.DUPLICATE_SAME_ACCESS", "name": "DUPLICATE_SAME_ACCESS", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 2}, "type_ref": "builtins.int"}}}, "DuplicateHandle": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 1], "arg_names": [null, null, null, null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_winapi.DuplicateHandle", "name": "Du<PERSON><PERSON><PERSON><PERSON>", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 1], "arg_names": [null, null, null, null, null, null], "arg_types": ["builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.bool", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "Du<PERSON><PERSON><PERSON><PERSON>", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ERROR_ACCESS_DENIED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 5, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "_winapi.ERROR_ACCESS_DENIED", "name": "ERROR_ACCESS_DENIED", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 5}, "type_ref": "builtins.int"}}}, "ERROR_ALREADY_EXISTS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 183, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "_winapi.ERROR_ALREADY_EXISTS", "name": "ERROR_ALREADY_EXISTS", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 183}, "type_ref": "builtins.int"}}}, "ERROR_BROKEN_PIPE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 109, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "_winapi.ERROR_BROKEN_PIPE", "name": "ERROR_BROKEN_PIPE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 109}, "type_ref": "builtins.int"}}}, "ERROR_IO_PENDING": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 997, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "_winapi.ERROR_IO_PENDING", "name": "ERROR_IO_PENDING", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 997}, "type_ref": "builtins.int"}}}, "ERROR_MORE_DATA": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 234, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "_winapi.ERROR_MORE_DATA", "name": "ERROR_MORE_DATA", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 234}, "type_ref": "builtins.int"}}}, "ERROR_NETNAME_DELETED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 64, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "_winapi.ERROR_NETNAME_DELETED", "name": "ERROR_NETNAME_DELETED", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 64}, "type_ref": "builtins.int"}}}, "ERROR_NO_DATA": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 232, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "_winapi.ERROR_NO_DATA", "name": "ERROR_NO_DATA", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 232}, "type_ref": "builtins.int"}}}, "ERROR_NO_SYSTEM_RESOURCES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 1450, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "_winapi.ERROR_NO_SYSTEM_RESOURCES", "name": "ERROR_NO_SYSTEM_RESOURCES", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 1450}, "type_ref": "builtins.int"}}}, "ERROR_OPERATION_ABORTED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 995, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "_winapi.ERROR_OPERATION_ABORTED", "name": "ERROR_OPERATION_ABORTED", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 995}, "type_ref": "builtins.int"}}}, "ERROR_PIPE_BUSY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 231, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "_winapi.ERROR_PIPE_BUSY", "name": "ERROR_PIPE_BUSY", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 231}, "type_ref": "builtins.int"}}}, "ERROR_PIPE_CONNECTED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 535, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "_winapi.ERROR_PIPE_CONNECTED", "name": "ERROR_PIPE_CONNECTED", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 535}, "type_ref": "builtins.int"}}}, "ERROR_PRIVILEGE_NOT_HELD": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 1314, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "_winapi.ERROR_PRIVILEGE_NOT_HELD", "name": "ERROR_PRIVILEGE_NOT_HELD", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 1314}, "type_ref": "builtins.int"}}}, "ERROR_SEM_TIMEOUT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 121, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "_winapi.ERROR_SEM_TIMEOUT", "name": "ERROR_SEM_TIMEOUT", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 121}, "type_ref": "builtins.int"}}}, "ExitProcess": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_winapi.ExitProcess", "name": "ExitProcess", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ExitProcess", "ret_type": {".class": "UninhabitedType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "FILE_FLAG_FIRST_PIPE_INSTANCE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 524288, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "_winapi.FILE_FLAG_FIRST_PIPE_INSTANCE", "name": "FILE_FLAG_FIRST_PIPE_INSTANCE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 524288}, "type_ref": "builtins.int"}}}, "FILE_FLAG_OVERLAPPED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 1073741824, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "_winapi.FILE_FLAG_OVERLAPPED", "name": "FILE_FLAG_OVERLAPPED", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 1073741824}, "type_ref": "builtins.int"}}}, "FILE_GENERIC_READ": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 1179785, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "_winapi.FILE_GENERIC_READ", "name": "FILE_GENERIC_READ", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 1179785}, "type_ref": "builtins.int"}}}, "FILE_GENERIC_WRITE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 1179926, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "_winapi.FILE_GENERIC_WRITE", "name": "FILE_GENERIC_WRITE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 1179926}, "type_ref": "builtins.int"}}}, "FILE_MAP_ALL_ACCESS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 983071, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "_winapi.FILE_MAP_ALL_ACCESS", "name": "FILE_MAP_ALL_ACCESS", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 983071}, "type_ref": "builtins.int"}}}, "FILE_MAP_COPY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 1, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "_winapi.FILE_MAP_COPY", "name": "FILE_MAP_COPY", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 1}, "type_ref": "builtins.int"}}}, "FILE_MAP_EXECUTE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 32, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "_winapi.FILE_MAP_EXECUTE", "name": "FILE_MAP_EXECUTE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 32}, "type_ref": "builtins.int"}}}, "FILE_MAP_READ": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 4, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "_winapi.FILE_MAP_READ", "name": "FILE_MAP_READ", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 4}, "type_ref": "builtins.int"}}}, "FILE_MAP_WRITE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 2, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "_winapi.FILE_MAP_WRITE", "name": "FILE_MAP_WRITE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 2}, "type_ref": "builtins.int"}}}, "FILE_TYPE_CHAR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 2, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "_winapi.FILE_TYPE_CHAR", "name": "FILE_TYPE_CHAR", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 2}, "type_ref": "builtins.int"}}}, "FILE_TYPE_DISK": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 1, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "_winapi.FILE_TYPE_DISK", "name": "FILE_TYPE_DISK", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 1}, "type_ref": "builtins.int"}}}, "FILE_TYPE_PIPE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 3, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "_winapi.FILE_TYPE_PIPE", "name": "FILE_TYPE_PIPE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 3}, "type_ref": "builtins.int"}}}, "FILE_TYPE_REMOTE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 32768, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "_winapi.FILE_TYPE_REMOTE", "name": "FILE_TYPE_REMOTE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 32768}, "type_ref": "builtins.int"}}}, "FILE_TYPE_UNKNOWN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 0, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "_winapi.FILE_TYPE_UNKNOWN", "name": "FILE_TYPE_UNKNOWN", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 0}, "type_ref": "builtins.int"}}}, "Final": {".class": "SymbolTableNode", "cross_ref": "typing.Final", "kind": "Gdef", "module_hidden": true, "module_public": false}, "GENERIC_READ": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 2147483648, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "_winapi.GENERIC_READ", "name": "GENERIC_READ", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 2147483648}, "type_ref": "builtins.int"}}}, "GENERIC_WRITE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 1073741824, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "_winapi.GENERIC_WRITE", "name": "GENERIC_WRITE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 1073741824}, "type_ref": "builtins.int"}}}, "GetACP": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_winapi.GetACP", "name": "GetACP", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "GetACP", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "GetCurrentProcess": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_winapi.GetCurrentProcess", "name": "GetCurrentProcess", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "GetCurrentProcess", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "GetExitCodeProcess": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_winapi.GetExitCodeProcess", "name": "GetExitCodeProcess", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "GetExitCodeProcess", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "GetFileType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["handle"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_winapi.GetFileType", "name": "GetFileType", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["handle"], "arg_types": ["builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "GetFileType", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "GetLastError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_winapi.GetLastError", "name": "GetLastError", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "GetLastError", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "GetModuleFileName": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_winapi.GetModuleFileName", "name": "GetModuleFileName", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "GetModuleFileName", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "GetStdHandle": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_winapi.GetStdHandle", "name": "GetStdHandle", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "GetStdHandle", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "GetVersion": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_winapi.GetVersion", "name": "GetVersion", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "GetVersion", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "HIGH_PRIORITY_CLASS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 128, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "_winapi.HIGH_PRIORITY_CLASS", "name": "HIGH_PRIORITY_CLASS", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 128}, "type_ref": "builtins.int"}}}, "IDLE_PRIORITY_CLASS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 64, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "_winapi.IDLE_PRIORITY_CLASS", "name": "IDLE_PRIORITY_CLASS", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 64}, "type_ref": "builtins.int"}}}, "INFINITE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 4294967295, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "_winapi.INFINITE", "name": "INFINITE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 4294967295}, "type_ref": "builtins.int"}}}, "INVALID_HANDLE_VALUE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 18446744073709551615, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "_winapi.INVALID_HANDLE_VALUE", "name": "INVALID_HANDLE_VALUE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 18446744073709551615}, "type_ref": "builtins.int"}}}, "LCMAP_FULLWIDTH": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_winapi.LCMAP_FULLWIDTH", "name": "LCMAP_FULLWIDTH", "type": "builtins.int"}}, "LCMAP_HALFWIDTH": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_winapi.LCMAP_HALFWIDTH", "name": "LCMAP_HALFWIDTH", "type": "builtins.int"}}, "LCMAP_HIRAGANA": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_winapi.LCMAP_HIRAGANA", "name": "LCMAP_HIRAGANA", "type": "builtins.int"}}, "LCMAP_KATAKANA": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_winapi.LCMAP_KATAKANA", "name": "LCMAP_KATAKANA", "type": "builtins.int"}}, "LCMAP_LINGUISTIC_CASING": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_winapi.LCMAP_LINGUISTIC_CASING", "name": "LCMAP_LINGUISTIC_CASING", "type": "builtins.int"}}, "LCMAP_LOWERCASE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_winapi.LCMAP_LOWERCASE", "name": "LCMAP_LOWERCASE", "type": "builtins.int"}}, "LCMAP_SIMPLIFIED_CHINESE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_winapi.LCMAP_SIMPLIFIED_CHINESE", "name": "LCMAP_SIMPLIFIED_CHINESE", "type": "builtins.int"}}, "LCMAP_TITLECASE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_winapi.LCMAP_TITLECASE", "name": "LCMAP_TITLECASE", "type": "builtins.int"}}, "LCMAP_TRADITIONAL_CHINESE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_winapi.LCMAP_TRADITIONAL_CHINESE", "name": "LCMAP_TRADITIONAL_CHINESE", "type": "builtins.int"}}, "LCMAP_UPPERCASE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_winapi.LCMAP_UPPERCASE", "name": "LCMAP_UPPERCASE", "type": "builtins.int"}}, "LCMapStringEx": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["locale", "flags", "src"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_winapi.LCMapStringEx", "name": "LCMapStringEx", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["locale", "flags", "src"], "arg_types": ["builtins.str", "builtins.int", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "LCMapStringEx", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "LOCALE_NAME_INVARIANT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_winapi.LOCALE_NAME_INVARIANT", "name": "LOCALE_NAME_INVARIANT", "type": "builtins.str"}}, "LOCALE_NAME_MAX_LENGTH": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_winapi.LOCALE_NAME_MAX_LENGTH", "name": "LOCALE_NAME_MAX_LENGTH", "type": "builtins.int"}}, "LOCALE_NAME_SYSTEM_DEFAULT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_winapi.LOCALE_NAME_SYSTEM_DEFAULT", "name": "LOCALE_NAME_SYSTEM_DEFAULT", "type": "builtins.str"}}, "LOCALE_NAME_USER_DEFAULT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_winapi.LOCALE_NAME_USER_DEFAULT", "name": "LOCALE_NAME_USER_DEFAULT", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef", "module_hidden": true, "module_public": false}, "MEM_COMMIT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 4096, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "_winapi.MEM_COMMIT", "name": "MEM_COMMIT", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 4096}, "type_ref": "builtins.int"}}}, "MEM_FREE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 65536, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "_winapi.MEM_FREE", "name": "MEM_FREE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 65536}, "type_ref": "builtins.int"}}}, "MEM_IMAGE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 16777216, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "_winapi.MEM_IMAGE", "name": "MEM_IMAGE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 16777216}, "type_ref": "builtins.int"}}}, "MEM_MAPPED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 262144, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "_winapi.MEM_MAPPED", "name": "MEM_MAPPED", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 262144}, "type_ref": "builtins.int"}}}, "MEM_PRIVATE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 131072, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "_winapi.MEM_PRIVATE", "name": "MEM_PRIVATE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 131072}, "type_ref": "builtins.int"}}}, "MEM_RESERVE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 8192, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "_winapi.MEM_RESERVE", "name": "MEM_RESERVE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 8192}, "type_ref": "builtins.int"}}}, "NMPWAIT_WAIT_FOREVER": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 4294967295, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "_winapi.NMPWAIT_WAIT_FOREVER", "name": "NMPWAIT_WAIT_FOREVER", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 4294967295}, "type_ref": "builtins.int"}}}, "NORMAL_PRIORITY_CLASS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 32, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "_winapi.NORMAL_PRIORITY_CLASS", "name": "NORMAL_PRIORITY_CLASS", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 32}, "type_ref": "builtins.int"}}}, "NULL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 0, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "_winapi.NULL", "name": "NULL", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 0}, "type_ref": "builtins.int"}}}, "NeedCurrentDirectoryForExePath": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_winapi.NeedCurrentDirectoryForExePath", "name": "NeedCurrentDirectoryForExePath", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "NeedCurrentDirectoryForExePath", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "NoReturn": {".class": "SymbolTableNode", "cross_ref": "typing.NoReturn", "kind": "Gdef", "module_hidden": true, "module_public": false}, "OPEN_EXISTING": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 3, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "_winapi.OPEN_EXISTING", "name": "OPEN_EXISTING", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 3}, "type_ref": "builtins.int"}}}, "OpenProcess": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_winapi.OpenProcess", "name": "OpenProcess", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": ["builtins.int", "builtins.bool", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "OpenProcess", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "Overlapped": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_winapi.Overlapped", "name": "Overlapped", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_final"], "fullname": "_winapi.Overlapped", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_winapi", "mro": ["_winapi.Overlapped", "builtins.object"], "names": {".class": "SymbolTable", "GetOverlappedResult": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_winapi.Overlapped.GetOverlappedResult", "name": "GetOverlappedResult", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["_winapi.Overlapped", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "GetOverlappedResult of Overlapped", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "cancel": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_winapi.Overlapped.cancel", "name": "cancel", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_winapi.Overlapped"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cancel of Overlapped", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "event": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "_winapi.Overlapped.event", "name": "event", "type": "builtins.int"}}, "getbuffer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_winapi.Overlapped.getbuffer", "name": "getbuffer", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["_winapi.Overlapped"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getbuffer of Overlapped", "ret_type": {".class": "UnionType", "items": ["builtins.bytes", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PAGE_EXECUTE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 16, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "_winapi.PAGE_EXECUTE", "name": "PAGE_EXECUTE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 16}, "type_ref": "builtins.int"}}}, "PAGE_EXECUTE_READ": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 32, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "_winapi.PAGE_EXECUTE_READ", "name": "PAGE_EXECUTE_READ", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 32}, "type_ref": "builtins.int"}}}, "PAGE_EXECUTE_READWRITE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 64, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "_winapi.PAGE_EXECUTE_READWRITE", "name": "PAGE_EXECUTE_READWRITE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 64}, "type_ref": "builtins.int"}}}, "PAGE_EXECUTE_WRITECOPY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 128, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "_winapi.PAGE_EXECUTE_WRITECOPY", "name": "PAGE_EXECUTE_WRITECOPY", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 128}, "type_ref": "builtins.int"}}}, "PAGE_GUARD": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 256, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "_winapi.PAGE_GUARD", "name": "PAGE_GUARD", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 256}, "type_ref": "builtins.int"}}}, "PAGE_NOACCESS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 1, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "_winapi.PAGE_NOACCESS", "name": "PAGE_NOACCESS", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 1}, "type_ref": "builtins.int"}}}, "PAGE_NOCACHE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 512, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "_winapi.PAGE_NOCACHE", "name": "PAGE_NOCACHE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 512}, "type_ref": "builtins.int"}}}, "PAGE_READONLY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 2, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "_winapi.PAGE_READONLY", "name": "PAGE_READONLY", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 2}, "type_ref": "builtins.int"}}}, "PAGE_READWRITE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 4, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "_winapi.PAGE_READWRITE", "name": "PAGE_READWRITE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 4}, "type_ref": "builtins.int"}}}, "PAGE_WRITECOMBINE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 1024, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "_winapi.PAGE_WRITECOMBINE", "name": "PAGE_WRITECOMBINE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 1024}, "type_ref": "builtins.int"}}}, "PAGE_WRITECOPY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 8, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "_winapi.PAGE_WRITECOPY", "name": "PAGE_WRITECOPY", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 8}, "type_ref": "builtins.int"}}}, "PIPE_ACCESS_DUPLEX": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 3, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "_winapi.PIPE_ACCESS_DUPLEX", "name": "PIPE_ACCESS_DUPLEX", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 3}, "type_ref": "builtins.int"}}}, "PIPE_ACCESS_INBOUND": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 1, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "_winapi.PIPE_ACCESS_INBOUND", "name": "PIPE_ACCESS_INBOUND", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 1}, "type_ref": "builtins.int"}}}, "PIPE_READMODE_MESSAGE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 2, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "_winapi.PIPE_READMODE_MESSAGE", "name": "PIPE_READMODE_MESSAGE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 2}, "type_ref": "builtins.int"}}}, "PIPE_TYPE_MESSAGE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 4, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "_winapi.PIPE_TYPE_MESSAGE", "name": "PIPE_TYPE_MESSAGE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 4}, "type_ref": "builtins.int"}}}, "PIPE_UNLIMITED_INSTANCES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 255, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "_winapi.PIPE_UNLIMITED_INSTANCES", "name": "PIPE_UNLIMITED_INSTANCES", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 255}, "type_ref": "builtins.int"}}}, "PIPE_WAIT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 0, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "_winapi.PIPE_WAIT", "name": "PIPE_WAIT", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 0}, "type_ref": "builtins.int"}}}, "PROCESS_ALL_ACCESS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 2097151, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "_winapi.PROCESS_ALL_ACCESS", "name": "PROCESS_ALL_ACCESS", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 2097151}, "type_ref": "builtins.int"}}}, "PROCESS_DUP_HANDLE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 64, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "_winapi.PROCESS_DUP_HANDLE", "name": "PROCESS_DUP_HANDLE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 64}, "type_ref": "builtins.int"}}}, "PeekNamedPipe": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_winapi.PeekNamedPipe", "name": "PeekNamedPipe", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": [null, null], "arg_types": ["builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "PeekNamedPipe", "ret_type": {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "TupleType", "implicit": false, "items": ["builtins.bytes", "builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "REALTIME_PRIORITY_CLASS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 256, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "_winapi.REALTIME_PRIORITY_CLASS", "name": "REALTIME_PRIORITY_CLASS", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 256}, "type_ref": "builtins.int"}}}, "ReadFile": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "_winapi.ReadFile", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["handle", "size", "overlapped"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "_winapi.ReadFile", "name": "ReadFile", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["handle", "size", "overlapped"], "arg_types": ["builtins.int", "builtins.int", {".class": "LiteralType", "fallback": "builtins.bool", "value": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ReadFile", "ret_type": {".class": "TupleType", "implicit": false, "items": ["_winapi.Overlapped", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "_winapi.ReadFile", "name": "ReadFile", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["handle", "size", "overlapped"], "arg_types": ["builtins.int", "builtins.int", {".class": "LiteralType", "fallback": "builtins.bool", "value": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ReadFile", "ret_type": {".class": "TupleType", "implicit": false, "items": ["_winapi.Overlapped", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["handle", "size", "overlapped"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "_winapi.ReadFile", "name": "ReadFile", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["handle", "size", "overlapped"], "arg_types": ["builtins.int", "builtins.int", {".class": "LiteralType", "fallback": "builtins.bool", "value": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ReadFile", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.bytes", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "_winapi.ReadFile", "name": "ReadFile", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["handle", "size", "overlapped"], "arg_types": ["builtins.int", "builtins.int", {".class": "LiteralType", "fallback": "builtins.bool", "value": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ReadFile", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.bytes", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["handle", "size", "overlapped"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "_winapi.ReadFile", "name": "ReadFile", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["handle", "size", "overlapped"], "arg_types": ["builtins.int", "builtins.int", {".class": "UnionType", "items": ["builtins.int", "builtins.bool"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ReadFile", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "_winapi.ReadFile", "name": "ReadFile", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["handle", "size", "overlapped"], "arg_types": ["builtins.int", "builtins.int", {".class": "UnionType", "items": ["builtins.int", "builtins.bool"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ReadFile", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["handle", "size", "overlapped"], "arg_types": ["builtins.int", "builtins.int", {".class": "LiteralType", "fallback": "builtins.bool", "value": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ReadFile", "ret_type": {".class": "TupleType", "implicit": false, "items": ["_winapi.Overlapped", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["handle", "size", "overlapped"], "arg_types": ["builtins.int", "builtins.int", {".class": "LiteralType", "fallback": "builtins.bool", "value": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ReadFile", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.bytes", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["handle", "size", "overlapped"], "arg_types": ["builtins.int", "builtins.int", {".class": "UnionType", "items": ["builtins.int", "builtins.bool"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ReadFile", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "ReadableBuffer": {".class": "SymbolTableNode", "cross_ref": "_typeshed.ReadableBuffer", "kind": "Gdef", "module_hidden": true, "module_public": false}, "SEC_COMMIT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": *********, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "_winapi.SEC_COMMIT", "name": "SEC_COMMIT", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": *********}, "type_ref": "builtins.int"}}}, "SEC_IMAGE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 16777216, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "_winapi.SEC_IMAGE", "name": "SEC_IMAGE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 16777216}, "type_ref": "builtins.int"}}}, "SEC_LARGE_PAGES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 2147483648, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "_winapi.SEC_LARGE_PAGES", "name": "SEC_LARGE_PAGES", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 2147483648}, "type_ref": "builtins.int"}}}, "SEC_NOCACHE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 268435456, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "_winapi.SEC_NOCACHE", "name": "SEC_NOCACHE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 268435456}, "type_ref": "builtins.int"}}}, "SEC_RESERVE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 67108864, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "_winapi.SEC_RESERVE", "name": "SEC_RESERVE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 67108864}, "type_ref": "builtins.int"}}}, "SEC_WRITECOMBINE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 1073741824, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "_winapi.SEC_WRITECOMBINE", "name": "SEC_WRITECOMBINE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 1073741824}, "type_ref": "builtins.int"}}}, "STARTF_USESHOWWINDOW": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 1, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "_winapi.STARTF_USESHOWWINDOW", "name": "STARTF_USESHOWWINDOW", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 1}, "type_ref": "builtins.int"}}}, "STARTF_USESTDHANDLES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 256, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "_winapi.STARTF_USESTDHANDLES", "name": "STARTF_USESTDHANDLES", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 256}, "type_ref": "builtins.int"}}}, "STD_ERROR_HANDLE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 4294967284, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "_winapi.STD_ERROR_HANDLE", "name": "STD_ERROR_HANDLE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 4294967284}, "type_ref": "builtins.int"}}}, "STD_INPUT_HANDLE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 4294967286, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "_winapi.STD_INPUT_HANDLE", "name": "STD_INPUT_HANDLE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 4294967286}, "type_ref": "builtins.int"}}}, "STD_OUTPUT_HANDLE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 4294967285, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "_winapi.STD_OUTPUT_HANDLE", "name": "STD_OUTPUT_HANDLE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 4294967285}, "type_ref": "builtins.int"}}}, "STILL_ACTIVE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 259, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "_winapi.STILL_ACTIVE", "name": "STILL_ACTIVE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 259}, "type_ref": "builtins.int"}}}, "SW_HIDE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 0, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "_winapi.SW_HIDE", "name": "SW_HIDE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 0}, "type_ref": "builtins.int"}}}, "SYNCHRONIZE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 1048576, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "_winapi.SYNCHRONIZE", "name": "SYNCHRONIZE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 1048576}, "type_ref": "builtins.int"}}}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef", "module_hidden": true, "module_public": false}, "SetNamedPipeHandleState": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": [null, null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_winapi.SetNamedPipeHandleState", "name": "SetNamedPipeHandleState", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": [null, null, null, null], "arg_types": ["builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "SetNamedPipeHandleState", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "TerminateProcess": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_winapi.TerminateProcess", "name": "TerminateProcess", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "TerminateProcess", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "UnmapViewOfFile": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_winapi.UnmapViewOfFile", "name": "UnmapViewOfFile", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "UnmapViewOfFile", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "WAIT_ABANDONED_0": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 128, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "_winapi.WAIT_ABANDONED_0", "name": "WAIT_ABANDONED_0", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 128}, "type_ref": "builtins.int"}}}, "WAIT_OBJECT_0": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 0, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "_winapi.WAIT_OBJECT_0", "name": "WAIT_OBJECT_0", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 0}, "type_ref": "builtins.int"}}}, "WAIT_TIMEOUT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 258, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "_winapi.WAIT_TIMEOUT", "name": "WAIT_TIMEOUT", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 258}, "type_ref": "builtins.int"}}}, "WaitForMultipleObjects": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": [null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_winapi.WaitForMultipleObjects", "name": "WaitForMultipleObjects", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": [null, null, null], "arg_types": [{".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "builtins.bool", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "WaitForMultipleObjects", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "WaitForSingleObject": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_winapi.WaitForSingleObject", "name": "WaitForSingleObject", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "WaitForSingleObject", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "WaitNamedPipe": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_winapi.WaitNamedPipe", "name": "WaitNamedPipe", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["builtins.str", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "WaitNamedPipe", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "WriteFile": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "_winapi.WriteFile", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["handle", "buffer", "overlapped"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "_winapi.WriteFile", "name": "WriteFile", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["handle", "buffer", "overlapped"], "arg_types": ["builtins.int", "_collections_abc.<PERSON><PERSON>er", {".class": "LiteralType", "fallback": "builtins.bool", "value": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "WriteFile", "ret_type": {".class": "TupleType", "implicit": false, "items": ["_winapi.Overlapped", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "_winapi.WriteFile", "name": "WriteFile", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["handle", "buffer", "overlapped"], "arg_types": ["builtins.int", "_collections_abc.<PERSON><PERSON>er", {".class": "LiteralType", "fallback": "builtins.bool", "value": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "WriteFile", "ret_type": {".class": "TupleType", "implicit": false, "items": ["_winapi.Overlapped", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["handle", "buffer", "overlapped"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "_winapi.WriteFile", "name": "WriteFile", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["handle", "buffer", "overlapped"], "arg_types": ["builtins.int", "_collections_abc.<PERSON><PERSON>er", {".class": "LiteralType", "fallback": "builtins.bool", "value": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "WriteFile", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "_winapi.WriteFile", "name": "WriteFile", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["handle", "buffer", "overlapped"], "arg_types": ["builtins.int", "_collections_abc.<PERSON><PERSON>er", {".class": "LiteralType", "fallback": "builtins.bool", "value": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "WriteFile", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["handle", "buffer", "overlapped"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "_winapi.WriteFile", "name": "WriteFile", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["handle", "buffer", "overlapped"], "arg_types": ["builtins.int", "_collections_abc.<PERSON><PERSON>er", {".class": "UnionType", "items": ["builtins.int", "builtins.bool"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "WriteFile", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "_winapi.WriteFile", "name": "WriteFile", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["handle", "buffer", "overlapped"], "arg_types": ["builtins.int", "_collections_abc.<PERSON><PERSON>er", {".class": "UnionType", "items": ["builtins.int", "builtins.bool"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "WriteFile", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["handle", "buffer", "overlapped"], "arg_types": ["builtins.int", "_collections_abc.<PERSON><PERSON>er", {".class": "LiteralType", "fallback": "builtins.bool", "value": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "WriteFile", "ret_type": {".class": "TupleType", "implicit": false, "items": ["_winapi.Overlapped", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["handle", "buffer", "overlapped"], "arg_types": ["builtins.int", "_collections_abc.<PERSON><PERSON>er", {".class": "LiteralType", "fallback": "builtins.bool", "value": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "WriteFile", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["handle", "buffer", "overlapped"], "arg_types": ["builtins.int", "_collections_abc.<PERSON><PERSON>er", {".class": "UnionType", "items": ["builtins.int", "builtins.bool"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "WriteFile", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_winapi.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_winapi.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_winapi.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_winapi.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_winapi.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_winapi.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "final": {".class": "SymbolTableNode", "cross_ref": "typing.final", "kind": "Gdef", "module_hidden": true, "module_public": false}, "overload": {".class": "SymbolTableNode", "cross_ref": "typing.overload", "kind": "Gdef", "module_hidden": true, "module_public": false}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.mypy-type-checker-2025.2.0\\bundled\\libs\\mypy\\typeshed\\stdlib\\_winapi.pyi"}