# Selenium Chrome - Google Script

Prosty skrypt Selenium do otwierania strony Google w przeglądarce Chrome.

## Pliki

- `google_selenium.py` - podstawowa wersja (wymaga ręcznego zarządzania ChromeDriver)
- `google_selenium_auto.py` - ulepszona wersja z automatycznym zarządzaniem ChromeDriver
- `requirements.txt` - lista wymaganych pakietów

## Instalacja

1. Zainstaluj wymagane pakiety:
```bash
pip install -r requirements.txt
```

2. <PERSON>ew<PERSON>j się, że masz zainstalowaną przeglądarkę Chrome

## Uruchomienie

### Opcja 1: Automatyczne zarządzanie ChromeDriver (zalecane)
```bash
python google_selenium_auto.py
```

### Opcja 2: Ręczne zarządzanie ChromeDriver
```bash
python google_selenium.py
```

## Co robi skrypt

1. Uruchamia przeglądarkę Chrome
2. Przechodzi na stronę https://www.google.com
3. Czeka aż strona się załaduje
4. Wyświetla informacje o stronie (tytuł, URL)
5. Czeka 5 sekund
6. Zamyka przeglądarkę

## Uwagi

- Skrypt `google_selenium_auto.py` automatycznie pobiera odpowiednią wersję ChromeDriver
- Jeśli chcesz uruchomić przeglądarkę w trybie headless (bez GUI), odkomentuj linię `chrome_options.add_argument("--headless")`
- Możesz rozszerzyć skrypt o dodatkowe funkcjonalności, np. wyszukiwanie
