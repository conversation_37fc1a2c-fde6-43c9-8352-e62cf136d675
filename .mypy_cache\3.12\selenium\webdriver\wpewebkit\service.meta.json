{"data_mtime": 1757882372, "dep_lines": [19, 19, 17, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 20, 10, 5, 30, 30, 30, 30, 30], "dependencies": ["selenium.webdriver.common.service", "selenium.webdriver.common", "typing", "builtins", "_frozen_importlib", "_io", "abc", "io", "os"], "hash": "bad7a0413f8b9a8fd84e6fa6e5b41beec72291bc", "id": "selenium.webdriver.wpewebkit.service", "ignore_all": true, "interface_hash": "5dbd81d2be0628e8d54c2e08275dba8e7759c47f", "mtime": 1757882171, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\selenium\\webdriver\\wpewebkit\\service.py", "plugin_data": null, "size": 2292, "suppressed": [], "version_id": "1.15.0"}