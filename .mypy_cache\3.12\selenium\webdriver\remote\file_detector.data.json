{".class": "MypyFile", "_fullname": "selenium.webdriver.remote.file_detector", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ABCMeta": {".class": "SymbolTableNode", "cross_ref": "abc.ABCMeta", "kind": "Gdef"}, "AnyKey": {".class": "SymbolTableNode", "cross_ref": "selenium.types.AnyKey", "kind": "Gdef"}, "FileDetector": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["is_local_file", 1]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": "abc.ABCMeta", "defn": {".class": "ClassDef", "fullname": "selenium.webdriver.remote.file_detector.FileDetector", "name": "FileDetector", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract"], "fullname": "selenium.webdriver.remote.file_detector.FileDetector", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "selenium.webdriver.remote.file_detector", "mro": ["selenium.webdriver.remote.file_detector.FileDetector", "builtins.object"], "names": {".class": "SymbolTable", "is_local_file": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 2], "arg_names": ["self", "keys"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "selenium.webdriver.remote.file_detector.FileDetector.is_local_file", "name": "is_local_file", "type": {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": ["self", "keys"], "arg_types": ["selenium.webdriver.remote.file_detector.FileDetector", {".class": "TypeAliasType", "args": [], "type_ref": "selenium.types.AnyKey"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_local_file of FileDetector", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "selenium.webdriver.remote.file_detector.FileDetector.is_local_file", "name": "is_local_file", "type": {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": ["self", "keys"], "arg_types": ["selenium.webdriver.remote.file_detector.FileDetector", {".class": "TypeAliasType", "args": [], "type_ref": "selenium.types.AnyKey"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_local_file of FileDetector", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "selenium.webdriver.remote.file_detector.FileDetector.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "selenium.webdriver.remote.file_detector.FileDetector", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "LocalFileDetector": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["selenium.webdriver.remote.file_detector.FileDetector"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "selenium.webdriver.remote.file_detector.LocalFileDetector", "name": "LocalFileDetector", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "selenium.webdriver.remote.file_detector.LocalFileDetector", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "selenium.webdriver.remote.file_detector", "mro": ["selenium.webdriver.remote.file_detector.LocalFileDetector", "selenium.webdriver.remote.file_detector.FileDetector", "builtins.object"], "names": {".class": "SymbolTable", "is_local_file": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": ["self", "keys"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "selenium.webdriver.remote.file_detector.LocalFileDetector.is_local_file", "name": "is_local_file", "type": {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": ["self", "keys"], "arg_types": ["selenium.webdriver.remote.file_detector.LocalFileDetector", {".class": "TypeAliasType", "args": [], "type_ref": "selenium.types.AnyKey"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_local_file of LocalFileDetector", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "selenium.webdriver.remote.file_detector.LocalFileDetector.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "selenium.webdriver.remote.file_detector.LocalFileDetector", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "Path": {".class": "SymbolTableNode", "cross_ref": "pathlib.Path", "kind": "Gdef"}, "UselessFileDetector": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["selenium.webdriver.remote.file_detector.FileDetector"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "selenium.webdriver.remote.file_detector.UselessFileDetector", "name": "UselessFileDetector", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "selenium.webdriver.remote.file_detector.UselessFileDetector", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "selenium.webdriver.remote.file_detector", "mro": ["selenium.webdriver.remote.file_detector.UselessFileDetector", "selenium.webdriver.remote.file_detector.FileDetector", "builtins.object"], "names": {".class": "SymbolTable", "is_local_file": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": ["self", "keys"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "selenium.webdriver.remote.file_detector.UselessFileDetector.is_local_file", "name": "is_local_file", "type": {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": ["self", "keys"], "arg_types": ["selenium.webdriver.remote.file_detector.UselessFileDetector", {".class": "TypeAliasType", "args": [], "type_ref": "selenium.types.AnyKey"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_local_file of UselessFileDetector", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "selenium.webdriver.remote.file_detector.UselessFileDetector.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "selenium.webdriver.remote.file_detector.UselessFileDetector", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "selenium.webdriver.remote.file_detector.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "selenium.webdriver.remote.file_detector.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "selenium.webdriver.remote.file_detector.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "selenium.webdriver.remote.file_detector.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "selenium.webdriver.remote.file_detector.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "selenium.webdriver.remote.file_detector.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "abstractmethod": {".class": "SymbolTableNode", "cross_ref": "abc.abstractmethod", "kind": "Gdef"}, "keys_to_typing": {".class": "SymbolTableNode", "cross_ref": "selenium.webdriver.common.utils.keys_to_typing", "kind": "Gdef"}, "suppress": {".class": "SymbolTableNode", "cross_ref": "contextlib.suppress", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\selenium\\webdriver\\remote\\file_detector.py"}