{".class": "MypyFile", "_fullname": "xml.sax", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "ContentHandler": {".class": "SymbolTableNode", "cross_ref": "xml.sax.handler.ContentHandler", "kind": "Gdef"}, "ErrorHandler": {".class": "SymbolTableNode", "cross_ref": "xml.sax.handler.ErrorHandler", "kind": "Gdef"}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Protocol": {".class": "SymbolTableNode", "cross_ref": "typing.Protocol", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ReadableBuffer": {".class": "SymbolTableNode", "cross_ref": "_typeshed.ReadableBuffer", "kind": "Gdef", "module_hidden": true, "module_public": false}, "SAXException": {".class": "SymbolTableNode", "cross_ref": "xml.sax._exceptions.SAXException", "kind": "Gdef"}, "SAXNotRecognizedException": {".class": "SymbolTableNode", "cross_ref": "xml.sax._exceptions.SAXNotRecognizedException", "kind": "Gdef"}, "SAXNotSupportedException": {".class": "SymbolTableNode", "cross_ref": "xml.sax._exceptions.SAXNotSupportedException", "kind": "Gdef"}, "SAXParseException": {".class": "SymbolTableNode", "cross_ref": "xml.sax._exceptions.SAXParseException", "kind": "Gdef"}, "SAXReaderNotAvailable": {".class": "SymbolTableNode", "cross_ref": "xml.sax._exceptions.SAXReaderNotAvailable", "kind": "Gdef"}, "StrPath": {".class": "SymbolTableNode", "cross_ref": "_typeshed.StrPath", "kind": "Gdef", "module_hidden": true, "module_public": false}, "SupportsRead": {".class": "SymbolTableNode", "cross_ref": "_typeshed.SupportsRead", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TypeAlias": {".class": "SymbolTableNode", "cross_ref": "typing.TypeAlias", "kind": "Gdef", "module_hidden": true, "module_public": false}, "XMLReader": {".class": "SymbolTableNode", "cross_ref": "xml.sax.xmlreader.XMLReader", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_Source": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "xml.sax._Source", "line": 18, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.StrPath"}, {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "xml.sax._SupportsReadClose"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "xml.sax._SupportsReadClose"}], "uses_pep604_syntax": true}}}, "_SupportsReadClose": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_typeshed._T_co", "id": 1, "name": "_T_co", "namespace": "xml.sax._SupportsReadClose", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "_typeshed.SupportsRead"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "xml.sax._SupportsReadClose", "name": "_SupportsReadClose", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_typeshed._T_co", "id": 1, "name": "_T_co", "namespace": "xml.sax._SupportsReadClose", "upper_bound": "builtins.object", "values": [], "variance": 1}]}, "deletable_attributes": [], "deprecated": null, "flags": ["is_protocol"], "fullname": "xml.sax._SupportsReadClose", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "xml.sax", "mro": ["xml.sax._SupportsReadClose", "_typeshed.SupportsRead", "builtins.object"], "names": {".class": "SymbolTable", "close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "xml.sax._SupportsReadClose.close", "name": "close", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_typeshed._T_co", "id": 1, "name": "_T_co", "namespace": "xml.sax._SupportsReadClose", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "xml.sax._SupportsReadClose"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "close of _SupportsReadClose", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "xml.sax._SupportsReadClose.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_typeshed._T_co", "id": 1, "name": "_T_co", "namespace": "xml.sax._SupportsReadClose", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "xml.sax._SupportsReadClose"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["_T_co"], "typeddict_type": null}}, "_T_co": {".class": "SymbolTableNode", "cross_ref": "_typeshed._T_co", "kind": "Gdef", "module_hidden": true, "module_public": false}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "xml.sax.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "xml.sax.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "xml.sax.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "xml.sax.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "xml.sax.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "xml.sax.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "xml.sax.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_create_parser": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["parser_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "xml.sax._create_parser", "name": "_create_parser", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["parser_name"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_create_parser", "ret_type": "xml.sax.xmlreader.XMLReader", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "default_parser_list": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "xml.sax.default_parser_list", "name": "default_parser_list", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "make_parser": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1], "arg_names": ["parser_list"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "xml.sax.make_parser", "name": "make_parser", "type": {".class": "CallableType", "arg_kinds": [1], "arg_names": ["parser_list"], "arg_types": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "make_parser", "ret_type": "xml.sax.xmlreader.XMLReader", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parse": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["source", "handler", "<PERSON><PERSON><PERSON><PERSON>"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "xml.sax.parse", "name": "parse", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["source", "handler", "<PERSON><PERSON><PERSON><PERSON>"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "xml.sax._Source"}, "xml.sax.handler.ContentHandler", "xml.sax.handler.ErrorHandler"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parseString": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["string", "handler", "<PERSON><PERSON><PERSON><PERSON>"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "xml.sax.parseString", "name": "parseString", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["string", "handler", "<PERSON><PERSON><PERSON><PERSON>"], "arg_types": [{".class": "UnionType", "items": ["_collections_abc.<PERSON><PERSON>er", "builtins.str"], "uses_pep604_syntax": true}, "xml.sax.handler.ContentHandler", {".class": "UnionType", "items": ["xml.sax.handler.ErrorHandler", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parseString", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "path": "c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.mypy-type-checker-2025.2.0\\bundled\\libs\\mypy\\typeshed\\stdlib\\xml\\sax\\__init__.pyi"}