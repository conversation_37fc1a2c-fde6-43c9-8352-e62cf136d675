{".class": "MypyFile", "_fullname": "google_selenium_auto", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "By": {".class": "SymbolTableNode", "cross_ref": "selenium.webdriver.common.by.By", "kind": "Gdef"}, "ChromeDriverManager": {".class": "SymbolTableNode", "cross_ref": "webdriver_manager.chrome.ChromeDriverManager", "kind": "Gdef"}, "EC": {".class": "SymbolTableNode", "cross_ref": "selenium.webdriver.support.expected_conditions", "kind": "Gdef"}, "Options": {".class": "SymbolTableNode", "cross_ref": "selenium.webdriver.chrome.options.Options", "kind": "Gdef"}, "Service": {".class": "SymbolTableNode", "cross_ref": "selenium.webdriver.chrome.service.Service", "kind": "Gdef"}, "WebDriverWait": {".class": "SymbolTableNode", "cross_ref": "selenium.webdriver.support.wait.WebDriverWait", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google_selenium_auto.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google_selenium_auto.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google_selenium_auto.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google_selenium_auto.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google_selenium_auto.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google_selenium_auto.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "go_to_google": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google_selenium_auto.go_to_google", "name": "go_to_google", "type": null}}, "setup_chrome_driver": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google_selenium_auto.setup_chrome_driver", "name": "setup_chrome_driver", "type": null}}, "time": {".class": "SymbolTableNode", "cross_ref": "time", "kind": "Gdef"}, "webdriver": {".class": "SymbolTableNode", "cross_ref": "selenium.webdriver", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\Documents\\python\\chomikuj\\google_selenium_auto.py"}