{".class": "MypyFile", "_fullname": "winreg", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "CloseKey": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "winreg.<PERSON>", "name": "<PERSON><PERSON><PERSON>", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "winreg._KeyType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "<PERSON><PERSON><PERSON>", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ConnectRegistry": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "winreg.ConnectRegistry", "name": "ConnectRegistry", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [], "type_ref": "winreg._KeyType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ConnectRegistry", "ret_type": "winreg.HKEYType", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "CreateKey": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "winreg.<PERSON>", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "winreg._KeyType"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "<PERSON><PERSON><PERSON><PERSON>", "ret_type": "winreg.HKEYType", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "CreateKeyEx": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["key", "sub_key", "reserved", "access"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "winreg.C<PERSON>KeyEx", "name": "CreateKeyEx", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["key", "sub_key", "reserved", "access"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "winreg._KeyType"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "CreateKeyEx", "ret_type": "winreg.HKEYType", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "DeleteKey": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "winreg.<PERSON>", "name": "DeleteKey", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "winreg._KeyType"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "DeleteKey", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "DeleteKeyEx": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["key", "sub_key", "access", "reserved"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "winreg.DeleteKeyEx", "name": "DeleteKeyEx", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["key", "sub_key", "access", "reserved"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "winreg._KeyType"}, "builtins.str", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "DeleteKeyEx", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "DeleteValue": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "winreg.DeleteValue", "name": "DeleteValue", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "winreg._KeyType"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "DeleteValue", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "DisableReflectionKey": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "winreg.DisableReflectionKey", "name": "DisableReflectionKey", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "winreg._KeyType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "DisableReflectionKey", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "EnableReflectionKey": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "winreg.EnableReflectionKey", "name": "EnableReflectionKey", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "winreg._KeyType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "EnableReflectionKey", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "EnumKey": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "winreg.<PERSON>", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "winreg._KeyType"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "<PERSON><PERSON><PERSON><PERSON>", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "EnumValue": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "winreg.<PERSON>um<PERSON>ue", "name": "EnumValue", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "winreg._KeyType"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "EnumValue", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ExpandEnvironmentStrings": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "winreg.ExpandEnvironmentStrings", "name": "ExpandEnvironmentStrings", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ExpandEnvironmentStrings", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "Final": {".class": "SymbolTableNode", "cross_ref": "typing.Final", "kind": "Gdef", "module_hidden": true, "module_public": false}, "FlushKey": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "winreg.<PERSON>", "name": "FlushKey", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "winreg._KeyType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "FlushKey", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "HKEYType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "winreg.HKEYType", "name": "HKEYType", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_final"], "fullname": "winreg.HKEYType", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "winreg", "mro": ["winreg.HKEYType", "builtins.object"], "names": {".class": "SymbolTable", "Close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "winreg.HKEYType.Close", "name": "Close", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["winreg.HKEYType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "Close of HKEYType", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "Detach": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "winreg.HKEYType.Detach", "name": "<PERSON><PERSON>", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["winreg.HKEYType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "Detach of HKEYType", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__bool__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "winreg.HKEYType.__bool__", "name": "__bool__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["winreg.HKEYType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__bool__ of HKEYType", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__enter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "winreg.HKEYType.__enter__", "name": "__enter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "winreg.HKEYType.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "winreg.HKEYType", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__enter__ of HKEYType", "ret_type": "winreg.HKEYType", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "winreg.HKEYType.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "winreg.HKEYType", "values": [], "variance": 0}]}}}, "__exit__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": [null, null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "winreg.HKEYType.__exit__", "name": "__exit__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": [null, null, null, null], "arg_types": ["winreg.HKEYType", {".class": "UnionType", "items": [{".class": "TypeType", "item": "builtins.BaseException"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.BaseException", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["types.TracebackType", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__exit__ of HKEYType", "ret_type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "winreg.HKEYType.__hash__", "name": "__hash__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["winreg.HKEYType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__hash__ of HKEYType", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__int__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "winreg.HKEYType.__int__", "name": "__int__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["winreg.HKEYType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__int__ of HKEYType", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "handle": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "winreg.HKEYType.handle", "name": "handle", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["winreg.HKEYType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "handle of HKEYType", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "winreg.HKEYType.handle", "name": "handle", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["winreg.HKEYType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "handle of HKEYType", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "winreg.HKEYType.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "winreg.HKEYType", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HKEY_CLASSES_ROOT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "winreg.HKEY_CLASSES_ROOT", "name": "HKEY_CLASSES_ROOT", "type": "builtins.int"}}, "HKEY_CURRENT_CONFIG": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "winreg.HKEY_CURRENT_CONFIG", "name": "HKEY_CURRENT_CONFIG", "type": "builtins.int"}}, "HKEY_CURRENT_USER": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "winreg.HKEY_CURRENT_USER", "name": "HKEY_CURRENT_USER", "type": "builtins.int"}}, "HKEY_DYN_DATA": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "winreg.HKEY_DYN_DATA", "name": "HKEY_DYN_DATA", "type": "builtins.int"}}, "HKEY_LOCAL_MACHINE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "winreg.HKEY_LOCAL_MACHINE", "name": "HKEY_LOCAL_MACHINE", "type": "builtins.int"}}, "HKEY_PERFORMANCE_DATA": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "winreg.HKEY_PERFORMANCE_DATA", "name": "HKEY_PERFORMANCE_DATA", "type": "builtins.int"}}, "HKEY_USERS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "winreg.HKEY_USERS", "name": "HKEY_USERS", "type": "builtins.int"}}, "KEY_ALL_ACCESS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 983103, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "winreg.<PERSON><PERSON><PERSON>_ALL_ACCESS", "name": "KEY_ALL_ACCESS", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 983103}, "type_ref": "builtins.int"}}}, "KEY_CREATE_LINK": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 32, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "winreg.KEY_CREATE_LINK", "name": "KEY_CREATE_LINK", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 32}, "type_ref": "builtins.int"}}}, "KEY_CREATE_SUB_KEY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 4, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "winreg.KEY_CREATE_SUB_KEY", "name": "KEY_CREATE_SUB_KEY", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 4}, "type_ref": "builtins.int"}}}, "KEY_ENUMERATE_SUB_KEYS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 8, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "winreg.KEY_ENUMERATE_SUB_KEYS", "name": "KEY_ENUMERATE_SUB_KEYS", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 8}, "type_ref": "builtins.int"}}}, "KEY_EXECUTE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 131097, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "winreg.KEY_EXECUTE", "name": "KEY_EXECUTE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 131097}, "type_ref": "builtins.int"}}}, "KEY_NOTIFY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 16, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "winreg.KEY_NOTIFY", "name": "KEY_NOTIFY", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 16}, "type_ref": "builtins.int"}}}, "KEY_QUERY_VALUE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 1, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "winreg.KEY_QUERY_VALUE", "name": "KEY_QUERY_VALUE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 1}, "type_ref": "builtins.int"}}}, "KEY_READ": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 131097, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "winreg.KEY_READ", "name": "KEY_READ", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 131097}, "type_ref": "builtins.int"}}}, "KEY_SET_VALUE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 2, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "winreg.KEY_SET_VALUE", "name": "KEY_SET_VALUE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 2}, "type_ref": "builtins.int"}}}, "KEY_WOW64_32KEY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 512, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "winreg.KEY_WOW64_32KEY", "name": "KEY_WOW64_32KEY", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 512}, "type_ref": "builtins.int"}}}, "KEY_WOW64_64KEY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 256, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "winreg.KEY_WOW64_64KEY", "name": "KEY_WOW64_64KEY", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 256}, "type_ref": "builtins.int"}}}, "KEY_WRITE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 131078, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "winreg.KEY_WRITE", "name": "KEY_WRITE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 131078}, "type_ref": "builtins.int"}}}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef", "module_hidden": true, "module_public": false}, "LoadKey": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "winreg.<PERSON>", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "winreg._KeyType"}, "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "<PERSON><PERSON><PERSON><PERSON>", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "OpenKey": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["key", "sub_key", "reserved", "access"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "winreg.<PERSON><PERSON>", "name": "Open<PERSON>ey", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["key", "sub_key", "reserved", "access"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "winreg._KeyType"}, "builtins.str", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "Open<PERSON>ey", "ret_type": "winreg.HKEYType", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "OpenKeyEx": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["key", "sub_key", "reserved", "access"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "winreg.OpenKeyEx", "name": "OpenKeyEx", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["key", "sub_key", "reserved", "access"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "winreg._KeyType"}, "builtins.str", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "OpenKeyEx", "ret_type": "winreg.HKEYType", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "QueryInfoKey": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "winreg.QueryInfoKey", "name": "QueryInfoKey", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "winreg._KeyType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "QueryInfoKey", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "QueryReflectionKey": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "winreg.QueryReflectionKey", "name": "QueryReflectionKey", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "winreg._KeyType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "QueryReflectionKey", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "QueryValue": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "winreg.QueryValue", "name": "QueryValue", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "winreg._KeyType"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "QueryValue", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "QueryValueEx": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "winreg.QueryValueEx", "name": "QueryValueEx", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "winreg._KeyType"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "QueryValueEx", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "REG_BINARY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 3, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "winreg.REG_BINARY", "name": "REG_BINARY", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 3}, "type_ref": "builtins.int"}}}, "REG_CREATED_NEW_KEY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 1, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "winreg.REG_CREATED_NEW_KEY", "name": "REG_CREATED_NEW_KEY", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 1}, "type_ref": "builtins.int"}}}, "REG_DWORD": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 4, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "winreg.REG_DWORD", "name": "REG_DWORD", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 4}, "type_ref": "builtins.int"}}}, "REG_DWORD_BIG_ENDIAN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 5, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "winreg.REG_DWORD_BIG_ENDIAN", "name": "REG_DWORD_BIG_ENDIAN", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 5}, "type_ref": "builtins.int"}}}, "REG_DWORD_LITTLE_ENDIAN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 4, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "winreg.REG_DWORD_LITTLE_ENDIAN", "name": "REG_DWORD_LITTLE_ENDIAN", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 4}, "type_ref": "builtins.int"}}}, "REG_EXPAND_SZ": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 2, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "winreg.REG_EXPAND_SZ", "name": "REG_EXPAND_SZ", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 2}, "type_ref": "builtins.int"}}}, "REG_FULL_RESOURCE_DESCRIPTOR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 9, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "winreg.REG_FULL_RESOURCE_DESCRIPTOR", "name": "REG_FULL_RESOURCE_DESCRIPTOR", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 9}, "type_ref": "builtins.int"}}}, "REG_LEGAL_CHANGE_FILTER": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 268435471, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "winreg.REG_LEGAL_CHANGE_FILTER", "name": "REG_LEGAL_CHANGE_FILTER", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 268435471}, "type_ref": "builtins.int"}}}, "REG_LEGAL_OPTION": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 31, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "winreg.REG_LEGAL_OPTION", "name": "REG_LEGAL_OPTION", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 31}, "type_ref": "builtins.int"}}}, "REG_LINK": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 6, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "winreg.REG_LINK", "name": "REG_LINK", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 6}, "type_ref": "builtins.int"}}}, "REG_MULTI_SZ": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 7, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "winreg.REG_MULTI_SZ", "name": "REG_MULTI_SZ", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 7}, "type_ref": "builtins.int"}}}, "REG_NONE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 0, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "winreg.REG_NONE", "name": "REG_NONE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 0}, "type_ref": "builtins.int"}}}, "REG_NOTIFY_CHANGE_ATTRIBUTES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 2, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "winreg.REG_NOTIFY_CHANGE_ATTRIBUTES", "name": "REG_NOTIFY_CHANGE_ATTRIBUTES", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 2}, "type_ref": "builtins.int"}}}, "REG_NOTIFY_CHANGE_LAST_SET": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 4, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "winreg.REG_NOTIFY_CHANGE_LAST_SET", "name": "REG_NOTIFY_CHANGE_LAST_SET", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 4}, "type_ref": "builtins.int"}}}, "REG_NOTIFY_CHANGE_NAME": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 1, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "winreg.REG_NOTIFY_CHANGE_NAME", "name": "REG_NOTIFY_CHANGE_NAME", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 1}, "type_ref": "builtins.int"}}}, "REG_NOTIFY_CHANGE_SECURITY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 8, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "winreg.REG_NOTIFY_CHANGE_SECURITY", "name": "REG_NOTIFY_CHANGE_SECURITY", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 8}, "type_ref": "builtins.int"}}}, "REG_NO_LAZY_FLUSH": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 4, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "winreg.REG_NO_LAZY_FLUSH", "name": "REG_NO_LAZY_FLUSH", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 4}, "type_ref": "builtins.int"}}}, "REG_OPENED_EXISTING_KEY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 2, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "winreg.REG_OPENED_EXISTING_KEY", "name": "REG_OPENED_EXISTING_KEY", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 2}, "type_ref": "builtins.int"}}}, "REG_OPTION_BACKUP_RESTORE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 4, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "winreg.REG_OPTION_BACKUP_RESTORE", "name": "REG_OPTION_BACKUP_RESTORE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 4}, "type_ref": "builtins.int"}}}, "REG_OPTION_CREATE_LINK": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 2, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "winreg.REG_OPTION_CREATE_LINK", "name": "REG_OPTION_CREATE_LINK", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 2}, "type_ref": "builtins.int"}}}, "REG_OPTION_NON_VOLATILE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 0, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "winreg.REG_OPTION_NON_VOLATILE", "name": "REG_OPTION_NON_VOLATILE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 0}, "type_ref": "builtins.int"}}}, "REG_OPTION_OPEN_LINK": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 8, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "winreg.REG_OPTION_OPEN_LINK", "name": "REG_OPTION_OPEN_LINK", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 8}, "type_ref": "builtins.int"}}}, "REG_OPTION_RESERVED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 0, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "winreg.REG_OPTION_RESERVED", "name": "REG_OPTION_RESERVED", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 0}, "type_ref": "builtins.int"}}}, "REG_OPTION_VOLATILE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 1, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "winreg.REG_OPTION_VOLATILE", "name": "REG_OPTION_VOLATILE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 1}, "type_ref": "builtins.int"}}}, "REG_QWORD": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 11, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "winreg.REG_QWORD", "name": "REG_QWORD", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 11}, "type_ref": "builtins.int"}}}, "REG_QWORD_LITTLE_ENDIAN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 11, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "winreg.REG_QWORD_LITTLE_ENDIAN", "name": "REG_QWORD_LITTLE_ENDIAN", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 11}, "type_ref": "builtins.int"}}}, "REG_REFRESH_HIVE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 2, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "winreg.REG_REFRESH_HIVE", "name": "REG_REFRESH_HIVE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 2}, "type_ref": "builtins.int"}}}, "REG_RESOURCE_LIST": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 8, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "winreg.REG_RESOURCE_LIST", "name": "REG_RESOURCE_LIST", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 8}, "type_ref": "builtins.int"}}}, "REG_RESOURCE_REQUIREMENTS_LIST": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 10, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "winreg.REG_RESOURCE_REQUIREMENTS_LIST", "name": "REG_RESOURCE_REQUIREMENTS_LIST", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 10}, "type_ref": "builtins.int"}}}, "REG_SZ": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 1, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "winreg.REG_SZ", "name": "REG_SZ", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 1}, "type_ref": "builtins.int"}}}, "REG_WHOLE_HIVE_VOLATILE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 1, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "winreg.REG_WHOLE_HIVE_VOLATILE", "name": "REG_WHOLE_HIVE_VOLATILE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 1}, "type_ref": "builtins.int"}}}, "ReadableBuffer": {".class": "SymbolTableNode", "cross_ref": "_typeshed.ReadableBuffer", "kind": "Gdef", "module_hidden": true, "module_public": false}, "SaveKey": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "winreg.<PERSON>", "name": "<PERSON><PERSON><PERSON>", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "winreg._KeyType"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "<PERSON><PERSON><PERSON>", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "Self": {".class": "SymbolTableNode", "cross_ref": "typing.Self", "kind": "Gdef", "module_hidden": true, "module_public": false}, "SetValue": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": [null, null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "winreg.SetValue", "name": "SetValue", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": [null, null, null, null], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "winreg._KeyType"}, "builtins.str", "builtins.int", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "SetValue", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "SetValueEx": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "winreg.SetValueEx", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": [null, null, null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "winreg.SetValueEx", "name": "SetValueEx", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": [null, null, null, null, null], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "winreg._KeyType"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.object", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.int", "value": 4}, {".class": "LiteralType", "fallback": "builtins.int", "value": 5}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "SetValueEx", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "winreg.SetValueEx", "name": "SetValueEx", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": [null, null, null, null, null], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "winreg._KeyType"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.object", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.int", "value": 4}, {".class": "LiteralType", "fallback": "builtins.int", "value": 5}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "SetValueEx", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": [null, null, null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "winreg.SetValueEx", "name": "SetValueEx", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": [null, null, null, null, null], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "winreg._KeyType"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.object", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.int", "value": 1}, {".class": "LiteralType", "fallback": "builtins.int", "value": 2}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "SetValueEx", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "winreg.SetValueEx", "name": "SetValueEx", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": [null, null, null, null, null], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "winreg._KeyType"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.object", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.int", "value": 1}, {".class": "LiteralType", "fallback": "builtins.int", "value": 2}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "SetValueEx", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": [null, null, null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "winreg.SetValueEx", "name": "SetValueEx", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": [null, null, null, null, null], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "winreg._KeyType"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.object", {".class": "LiteralType", "fallback": "builtins.int", "value": 7}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "SetValueEx", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "winreg.SetValueEx", "name": "SetValueEx", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": [null, null, null, null, null], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "winreg._KeyType"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.object", {".class": "LiteralType", "fallback": "builtins.int", "value": 7}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "SetValueEx", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": [null, null, null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "winreg.SetValueEx", "name": "SetValueEx", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": [null, null, null, null, null], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "winreg._KeyType"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.object", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.int", "value": 0}, {".class": "LiteralType", "fallback": "builtins.int", "value": 3}, {".class": "LiteralType", "fallback": "builtins.int", "value": 8}, {".class": "LiteralType", "fallback": "builtins.int", "value": 9}, {".class": "LiteralType", "fallback": "builtins.int", "value": 10}, {".class": "LiteralType", "fallback": "builtins.int", "value": 11}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["_collections_abc.<PERSON><PERSON>er", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "SetValueEx", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "winreg.SetValueEx", "name": "SetValueEx", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": [null, null, null, null, null], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "winreg._KeyType"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.object", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.int", "value": 0}, {".class": "LiteralType", "fallback": "builtins.int", "value": 3}, {".class": "LiteralType", "fallback": "builtins.int", "value": 8}, {".class": "LiteralType", "fallback": "builtins.int", "value": 9}, {".class": "LiteralType", "fallback": "builtins.int", "value": 10}, {".class": "LiteralType", "fallback": "builtins.int", "value": 11}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["_collections_abc.<PERSON><PERSON>er", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "SetValueEx", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": [null, null, null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "winreg.SetValueEx", "name": "SetValueEx", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": [null, null, null, null, null], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "winreg._KeyType"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.object", "builtins.int", {".class": "UnionType", "items": ["builtins.int", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "_collections_abc.<PERSON><PERSON>er", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "SetValueEx", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "winreg.SetValueEx", "name": "SetValueEx", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": [null, null, null, null, null], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "winreg._KeyType"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.object", "builtins.int", {".class": "UnionType", "items": ["builtins.int", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "_collections_abc.<PERSON><PERSON>er", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "SetValueEx", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": [null, null, null, null, null], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "winreg._KeyType"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.object", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.int", "value": 4}, {".class": "LiteralType", "fallback": "builtins.int", "value": 5}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "SetValueEx", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": [null, null, null, null, null], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "winreg._KeyType"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.object", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.int", "value": 1}, {".class": "LiteralType", "fallback": "builtins.int", "value": 2}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "SetValueEx", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": [null, null, null, null, null], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "winreg._KeyType"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.object", {".class": "LiteralType", "fallback": "builtins.int", "value": 7}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "SetValueEx", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": [null, null, null, null, null], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "winreg._KeyType"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.object", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.int", "value": 0}, {".class": "LiteralType", "fallback": "builtins.int", "value": 3}, {".class": "LiteralType", "fallback": "builtins.int", "value": 8}, {".class": "LiteralType", "fallback": "builtins.int", "value": 9}, {".class": "LiteralType", "fallback": "builtins.int", "value": 10}, {".class": "LiteralType", "fallback": "builtins.int", "value": 11}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["_collections_abc.<PERSON><PERSON>er", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "SetValueEx", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": [null, null, null, null, null], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "winreg._KeyType"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.object", "builtins.int", {".class": "UnionType", "items": ["builtins.int", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "_collections_abc.<PERSON><PERSON>er", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "SetValueEx", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "TracebackType": {".class": "SymbolTableNode", "cross_ref": "types.TracebackType", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TypeAlias": {".class": "SymbolTableNode", "cross_ref": "typing.TypeAlias", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Unused": {".class": "SymbolTableNode", "cross_ref": "_typeshed.Unused", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_KeyType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "winreg._KeyType", "line": 8, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["winreg.HKEYType", "builtins.int"], "uses_pep604_syntax": true}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "winreg.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "winreg.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "winreg.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "winreg.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "winreg.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "winreg.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "error": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "winreg.error", "line": 117, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "builtins.OSError"}}, "final": {".class": "SymbolTableNode", "cross_ref": "typing.final", "kind": "Gdef", "module_hidden": true, "module_public": false}, "overload": {".class": "SymbolTableNode", "cross_ref": "typing.overload", "kind": "Gdef", "module_hidden": true, "module_public": false}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.mypy-type-checker-2025.2.0\\bundled\\libs\\mypy\\typeshed\\stdlib\\winreg.pyi"}