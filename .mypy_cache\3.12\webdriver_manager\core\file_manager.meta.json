{"data_mtime": 1757882370, "dep_lines": [6, 7, 1, 2, 3, 4, 1, 1, 1, 1], "dep_prios": [5, 5, 10, 10, 10, 10, 5, 30, 30, 30], "dependencies": ["webdriver_manager.core.archive", "webdriver_manager.core.os_manager", "os", "re", "tarfile", "zipfile", "builtins", "_frozen_importlib", "abc", "typing"], "hash": "56bd889513a9c4a2ff03e35baf5fd06336fa5519", "id": "webdriver_manager.core.file_manager", "ignore_all": true, "interface_hash": "cc4ddaf528f8482432d80189f062992a1ef12877", "mtime": 1757882167, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\webdriver_manager\\core\\file_manager.py", "plugin_data": null, "size": 3629, "suppressed": [], "version_id": "1.15.0"}