{"data_mtime": 1757882371, "dep_lines": [20, 22, 23, 24, 19, 17, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30], "dependencies": ["selenium.webdriver.common.actions.interaction", "selenium.webdriver.common.actions.key_input", "selenium.webdriver.common.actions.pointer_input", "selenium.webdriver.common.actions.wheel_input", "selenium.webdriver.common.utils", "__future__", "builtins", "_frozen_importlib", "abc", "selenium.webdriver.common.actions.input_device", "typing"], "hash": "9b44bcd07157ce24c40d4e75f3ec2333de782b7f", "id": "selenium.webdriver.common.actions.key_actions", "ignore_all": true, "interface_hash": "9bddd567bbd1036a5fe011abdc3e817e7fba5c61", "mtime": 1757882171, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\selenium\\webdriver\\common\\actions\\key_actions.py", "plugin_data": null, "size": 2007, "suppressed": [], "version_id": "1.15.0"}