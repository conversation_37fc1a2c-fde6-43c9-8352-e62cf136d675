{".class": "MypyFile", "_fullname": "selenium.webdriver.remote.errorhandler", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "ElementClickInterceptedException": {".class": "SymbolTableNode", "cross_ref": "selenium.common.exceptions.ElementClickInterceptedException", "kind": "Gdef"}, "ElementNotInteractableException": {".class": "SymbolTableNode", "cross_ref": "selenium.common.exceptions.ElementNotInteractableException", "kind": "Gdef"}, "ElementNotSelectableException": {".class": "SymbolTableNode", "cross_ref": "selenium.common.exceptions.ElementNotSelectableException", "kind": "Gdef"}, "ElementNotVisibleException": {".class": "SymbolTableNode", "cross_ref": "selenium.common.exceptions.ElementNotVisibleException", "kind": "Gdef"}, "ErrorCode": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "selenium.webdriver.remote.errorhandler.ErrorCode", "name": "ErrorCode", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "selenium.webdriver.remote.errorhandler.ErrorCode", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "selenium.webdriver.remote.errorhandler", "mro": ["selenium.webdriver.remote.errorhandler.ErrorCode", "builtins.object"], "names": {".class": "SymbolTable", "ELEMENT_CLICK_INTERCEPTED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "selenium.webdriver.remote.errorhandler.ErrorCode.ELEMENT_CLICK_INTERCEPTED", "name": "ELEMENT_CLICK_INTERCEPTED", "type": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "ELEMENT_IS_NOT_SELECTABLE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "selenium.webdriver.remote.errorhandler.ErrorCode.ELEMENT_IS_NOT_SELECTABLE", "name": "ELEMENT_IS_NOT_SELECTABLE", "type": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "ELEMENT_NOT_INTERACTABLE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "selenium.webdriver.remote.errorhandler.ErrorCode.ELEMENT_NOT_INTERACTABLE", "name": "ELEMENT_NOT_INTERACTABLE", "type": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "ELEMENT_NOT_VISIBLE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "selenium.webdriver.remote.errorhandler.ErrorCode.ELEMENT_NOT_VISIBLE", "name": "ELEMENT_NOT_VISIBLE", "type": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "IME_ENGINE_ACTIVATION_FAILED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "selenium.webdriver.remote.errorhandler.ErrorCode.IME_ENGINE_ACTIVATION_FAILED", "name": "IME_ENGINE_ACTIVATION_FAILED", "type": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "IME_NOT_AVAILABLE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "selenium.webdriver.remote.errorhandler.ErrorCode.IME_NOT_AVAILABLE", "name": "IME_NOT_AVAILABLE", "type": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "INSECURE_CERTIFICATE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "selenium.webdriver.remote.errorhandler.ErrorCode.INSECURE_CERTIFICATE", "name": "INSECURE_CERTIFICATE", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "INVALID_ARGUMENT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "selenium.webdriver.remote.errorhandler.ErrorCode.INVALID_ARGUMENT", "name": "INVALID_ARGUMENT", "type": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "INVALID_COOKIE_DOMAIN": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "selenium.webdriver.remote.errorhandler.ErrorCode.INVALID_COOKIE_DOMAIN", "name": "INVALID_COOKIE_DOMAIN", "type": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "INVALID_COORDINATES": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "selenium.webdriver.remote.errorhandler.ErrorCode.INVALID_COORDINATES", "name": "INVALID_COORDINATES", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "INVALID_ELEMENT_COORDINATES": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "selenium.webdriver.remote.errorhandler.ErrorCode.INVALID_ELEMENT_COORDINATES", "name": "INVALID_ELEMENT_COORDINATES", "type": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "INVALID_ELEMENT_STATE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "selenium.webdriver.remote.errorhandler.ErrorCode.INVALID_ELEMENT_STATE", "name": "INVALID_ELEMENT_STATE", "type": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "INVALID_SELECTOR": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "selenium.webdriver.remote.errorhandler.ErrorCode.INVALID_SELECTOR", "name": "INVALID_SELECTOR", "type": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "INVALID_SESSION_ID": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "selenium.webdriver.remote.errorhandler.ErrorCode.INVALID_SESSION_ID", "name": "INVALID_SESSION_ID", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "INVALID_XPATH_SELECTOR": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "selenium.webdriver.remote.errorhandler.ErrorCode.INVALID_XPATH_SELECTOR", "name": "INVALID_XPATH_SELECTOR", "type": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "INVALID_XPATH_SELECTOR_RETURN_TYPER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "selenium.webdriver.remote.errorhandler.ErrorCode.INVALID_XPATH_SELECTOR_RETURN_TYPER", "name": "INVALID_XPATH_SELECTOR_RETURN_TYPER", "type": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "JAVASCRIPT_ERROR": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "selenium.webdriver.remote.errorhandler.ErrorCode.JAVASCRIPT_ERROR", "name": "JAVASCRIPT_ERROR", "type": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "METHOD_NOT_ALLOWED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "selenium.webdriver.remote.errorhandler.ErrorCode.METHOD_NOT_ALLOWED", "name": "METHOD_NOT_ALLOWED", "type": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "MOVE_TARGET_OUT_OF_BOUNDS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "selenium.webdriver.remote.errorhandler.ErrorCode.MOVE_TARGET_OUT_OF_BOUNDS", "name": "MOVE_TARGET_OUT_OF_BOUNDS", "type": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "NO_ALERT_OPEN": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "selenium.webdriver.remote.errorhandler.ErrorCode.NO_ALERT_OPEN", "name": "NO_ALERT_OPEN", "type": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "NO_SUCH_COOKIE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "selenium.webdriver.remote.errorhandler.ErrorCode.NO_SUCH_COOKIE", "name": "NO_SUCH_COOKIE", "type": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "NO_SUCH_ELEMENT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "selenium.webdriver.remote.errorhandler.ErrorCode.NO_SUCH_ELEMENT", "name": "NO_SUCH_ELEMENT", "type": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "NO_SUCH_FRAME": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "selenium.webdriver.remote.errorhandler.ErrorCode.NO_SUCH_FRAME", "name": "NO_SUCH_FRAME", "type": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "NO_SUCH_SHADOW_ROOT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "selenium.webdriver.remote.errorhandler.ErrorCode.NO_SUCH_SHADOW_ROOT", "name": "NO_SUCH_SHADOW_ROOT", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "NO_SUCH_WINDOW": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "selenium.webdriver.remote.errorhandler.ErrorCode.NO_SUCH_WINDOW", "name": "NO_SUCH_WINDOW", "type": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "SCRIPT_TIMEOUT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "selenium.webdriver.remote.errorhandler.ErrorCode.SCRIPT_TIMEOUT", "name": "SCRIPT_TIMEOUT", "type": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "SESSION_NOT_CREATED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "selenium.webdriver.remote.errorhandler.ErrorCode.SESSION_NOT_CREATED", "name": "SESSION_NOT_CREATED", "type": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "STALE_ELEMENT_REFERENCE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "selenium.webdriver.remote.errorhandler.ErrorCode.STALE_ELEMENT_REFERENCE", "name": "STALE_ELEMENT_REFERENCE", "type": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "SUCCESS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "selenium.webdriver.remote.errorhandler.ErrorCode.SUCCESS", "name": "SUCCESS", "type": "builtins.int"}}, "TIMEOUT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "selenium.webdriver.remote.errorhandler.ErrorCode.TIMEOUT", "name": "TIMEOUT", "type": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "UNABLE_TO_CAPTURE_SCREEN": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "selenium.webdriver.remote.errorhandler.ErrorCode.UNABLE_TO_CAPTURE_SCREEN", "name": "UNABLE_TO_CAPTURE_SCREEN", "type": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "UNABLE_TO_SET_COOKIE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "selenium.webdriver.remote.errorhandler.ErrorCode.UNABLE_TO_SET_COOKIE", "name": "UNABLE_TO_SET_COOKIE", "type": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "UNEXPECTED_ALERT_OPEN": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "selenium.webdriver.remote.errorhandler.ErrorCode.UNEXPECTED_ALERT_OPEN", "name": "UNEXPECTED_ALERT_OPEN", "type": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "UNKNOWN_COMMAND": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "selenium.webdriver.remote.errorhandler.ErrorCode.UNKNOWN_COMMAND", "name": "UNKNOWN_COMMAND", "type": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "UNKNOWN_ERROR": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "selenium.webdriver.remote.errorhandler.ErrorCode.UNKNOWN_ERROR", "name": "UNKNOWN_ERROR", "type": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "UNKNOWN_METHOD": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "selenium.webdriver.remote.errorhandler.ErrorCode.UNKNOWN_METHOD", "name": "UNKNOWN_METHOD", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "XPATH_LOOKUP_ERROR": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "selenium.webdriver.remote.errorhandler.ErrorCode.XPATH_LOOKUP_ERROR", "name": "XPATH_LOOKUP_ERROR", "type": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.list"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "selenium.webdriver.remote.errorhandler.ErrorCode.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "selenium.webdriver.remote.errorhandler.ErrorCode", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ErrorHandler": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "selenium.webdriver.remote.errorhandler.ErrorHandler", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "selenium.webdriver.remote.errorhandler.ErrorHandler", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "selenium.webdriver.remote.errorhandler", "mro": ["selenium.webdriver.remote.errorhandler.ErrorHandler", "builtins.object"], "names": {".class": "SymbolTable", "check_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "response"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "selenium.webdriver.remote.errorhandler.ErrorHandler.check_response", "name": "check_response", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "response"], "arg_types": ["selenium.webdriver.remote.errorhandler.ErrorHandler", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "check_response of <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "selenium.webdriver.remote.errorhandler.ErrorHandler.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "selenium.webdriver.remote.errorhandler.ErrorHandler", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ExceptionMapping": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "selenium.webdriver.remote.errorhandler.ExceptionMapping", "name": "ExceptionMapping", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "selenium.webdriver.remote.errorhandler.ExceptionMapping", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "selenium.webdriver.remote.errorhandler", "mro": ["selenium.webdriver.remote.errorhandler.ExceptionMapping", "builtins.object"], "names": {".class": "SymbolTable", "ELEMENT_CLICK_INTERCEPTED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "selenium.webdriver.remote.errorhandler.ExceptionMapping.ELEMENT_CLICK_INTERCEPTED", "name": "ELEMENT_CLICK_INTERCEPTED", "type": {".class": "CallableType", "arg_kinds": [1, 1, 1], "arg_names": ["msg", "screen", "stacktrace"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": ["selenium.common.exceptions.ElementClickInterceptedException"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "selenium.common.exceptions.ElementClickInterceptedException", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ELEMENT_IS_NOT_SELECTABLE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "selenium.webdriver.remote.errorhandler.ExceptionMapping.ELEMENT_IS_NOT_SELECTABLE", "name": "ELEMENT_IS_NOT_SELECTABLE", "type": {".class": "CallableType", "arg_kinds": [1, 1, 1], "arg_names": ["msg", "screen", "stacktrace"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": ["selenium.common.exceptions.ElementNotSelectableException"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "selenium.common.exceptions.ElementNotSelectableException", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ELEMENT_NOT_INTERACTABLE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "selenium.webdriver.remote.errorhandler.ExceptionMapping.ELEMENT_NOT_INTERACTABLE", "name": "ELEMENT_NOT_INTERACTABLE", "type": {".class": "CallableType", "arg_kinds": [1, 1, 1], "arg_names": ["msg", "screen", "stacktrace"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": ["selenium.common.exceptions.ElementNotInteractableException"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "selenium.common.exceptions.ElementNotInteractableException", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ELEMENT_NOT_VISIBLE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "selenium.webdriver.remote.errorhandler.ExceptionMapping.ELEMENT_NOT_VISIBLE", "name": "ELEMENT_NOT_VISIBLE", "type": {".class": "CallableType", "arg_kinds": [1, 1, 1], "arg_names": ["msg", "screen", "stacktrace"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": ["selenium.common.exceptions.ElementNotVisibleException"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "selenium.common.exceptions.ElementNotVisibleException", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "IME_ENGINE_ACTIVATION_FAILED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "selenium.webdriver.remote.errorhandler.ExceptionMapping.IME_ENGINE_ACTIVATION_FAILED", "name": "IME_ENGINE_ACTIVATION_FAILED", "type": {".class": "CallableType", "arg_kinds": [1, 1, 1], "arg_names": ["msg", "screen", "stacktrace"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": ["selenium.common.exceptions.ImeActivationFailedException"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "selenium.common.exceptions.ImeActivationFailedException", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "IME_NOT_AVAILABLE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "selenium.webdriver.remote.errorhandler.ExceptionMapping.IME_NOT_AVAILABLE", "name": "IME_NOT_AVAILABLE", "type": {".class": "CallableType", "arg_kinds": [1, 1, 1], "arg_names": ["msg", "screen", "stacktrace"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": ["selenium.common.exceptions.ImeNotAvailableException"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "selenium.common.exceptions.ImeNotAvailableException", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "INSECURE_CERTIFICATE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "selenium.webdriver.remote.errorhandler.ExceptionMapping.INSECURE_CERTIFICATE", "name": "INSECURE_CERTIFICATE", "type": {".class": "CallableType", "arg_kinds": [1, 1, 1], "arg_names": ["msg", "screen", "stacktrace"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": ["selenium.common.exceptions.InsecureCertificateException"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "selenium.common.exceptions.InsecureCertificateException", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "INVALID_ARGUMENT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "selenium.webdriver.remote.errorhandler.ExceptionMapping.INVALID_ARGUMENT", "name": "INVALID_ARGUMENT", "type": {".class": "CallableType", "arg_kinds": [1, 1, 1], "arg_names": ["msg", "screen", "stacktrace"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": ["selenium.common.exceptions.InvalidArgumentException"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "selenium.common.exceptions.InvalidArgumentException", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "INVALID_COOKIE_DOMAIN": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "selenium.webdriver.remote.errorhandler.ExceptionMapping.INVALID_COOKIE_DOMAIN", "name": "INVALID_COOKIE_DOMAIN", "type": {".class": "CallableType", "arg_kinds": [1, 1, 1], "arg_names": ["msg", "screen", "stacktrace"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": ["selenium.common.exceptions.InvalidCookieDomainException"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "selenium.common.exceptions.InvalidCookieDomainException", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "INVALID_COORDINATES": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "selenium.webdriver.remote.errorhandler.ExceptionMapping.INVALID_COORDINATES", "name": "INVALID_COORDINATES", "type": {".class": "CallableType", "arg_kinds": [1, 1, 1], "arg_names": ["msg", "screen", "stacktrace"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": ["selenium.common.exceptions.InvalidCoordinatesException"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "selenium.common.exceptions.InvalidCoordinatesException", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "INVALID_ELEMENT_STATE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "selenium.webdriver.remote.errorhandler.ExceptionMapping.INVALID_ELEMENT_STATE", "name": "INVALID_ELEMENT_STATE", "type": {".class": "CallableType", "arg_kinds": [1, 1, 1], "arg_names": ["msg", "screen", "stacktrace"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": ["selenium.common.exceptions.InvalidElementStateException"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "selenium.common.exceptions.InvalidElementStateException", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "INVALID_SELECTOR": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "selenium.webdriver.remote.errorhandler.ExceptionMapping.INVALID_SELECTOR", "name": "INVALID_SELECTOR", "type": {".class": "CallableType", "arg_kinds": [1, 1, 1], "arg_names": ["msg", "screen", "stacktrace"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": ["selenium.common.exceptions.InvalidSelectorException"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "selenium.common.exceptions.InvalidSelectorException", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "INVALID_SESSION_ID": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "selenium.webdriver.remote.errorhandler.ExceptionMapping.INVALID_SESSION_ID", "name": "INVALID_SESSION_ID", "type": {".class": "CallableType", "arg_kinds": [1, 1, 1], "arg_names": ["msg", "screen", "stacktrace"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": ["selenium.common.exceptions.InvalidSessionIdException"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "selenium.common.exceptions.InvalidSessionIdException", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "INVALID_XPATH_SELECTOR": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "selenium.webdriver.remote.errorhandler.ExceptionMapping.INVALID_XPATH_SELECTOR", "name": "INVALID_XPATH_SELECTOR", "type": {".class": "CallableType", "arg_kinds": [1, 1, 1], "arg_names": ["msg", "screen", "stacktrace"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": ["selenium.common.exceptions.InvalidSelectorException"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "selenium.common.exceptions.InvalidSelectorException", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "INVALID_XPATH_SELECTOR_RETURN_TYPER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "selenium.webdriver.remote.errorhandler.ExceptionMapping.INVALID_XPATH_SELECTOR_RETURN_TYPER", "name": "INVALID_XPATH_SELECTOR_RETURN_TYPER", "type": {".class": "CallableType", "arg_kinds": [1, 1, 1], "arg_names": ["msg", "screen", "stacktrace"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": ["selenium.common.exceptions.InvalidSelectorException"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "selenium.common.exceptions.InvalidSelectorException", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "JAVASCRIPT_ERROR": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "selenium.webdriver.remote.errorhandler.ExceptionMapping.JAVASCRIPT_ERROR", "name": "JAVASCRIPT_ERROR", "type": {".class": "CallableType", "arg_kinds": [1, 1, 1], "arg_names": ["msg", "screen", "stacktrace"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": ["selenium.common.exceptions.JavascriptException"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "selenium.common.exceptions.JavascriptException", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "MOVE_TARGET_OUT_OF_BOUNDS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "selenium.webdriver.remote.errorhandler.ExceptionMapping.MOVE_TARGET_OUT_OF_BOUNDS", "name": "MOVE_TARGET_OUT_OF_BOUNDS", "type": {".class": "CallableType", "arg_kinds": [1, 1, 1], "arg_names": ["msg", "screen", "stacktrace"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": ["selenium.common.exceptions.MoveTargetOutOfBoundsException"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "selenium.common.exceptions.MoveTargetOutOfBoundsException", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "NO_ALERT_OPEN": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "selenium.webdriver.remote.errorhandler.ExceptionMapping.NO_ALERT_OPEN", "name": "NO_ALERT_OPEN", "type": {".class": "CallableType", "arg_kinds": [1, 1, 1], "arg_names": ["msg", "screen", "stacktrace"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": ["selenium.common.exceptions.NoAlertPresentException"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "selenium.common.exceptions.NoAlertPresentException", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "NO_SUCH_COOKIE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "selenium.webdriver.remote.errorhandler.ExceptionMapping.NO_SUCH_COOKIE", "name": "NO_SUCH_COOKIE", "type": {".class": "CallableType", "arg_kinds": [1, 1, 1], "arg_names": ["msg", "screen", "stacktrace"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": ["selenium.common.exceptions.NoSuchCookieException"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "selenium.common.exceptions.NoSuchCookieException", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "NO_SUCH_ELEMENT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "selenium.webdriver.remote.errorhandler.ExceptionMapping.NO_SUCH_ELEMENT", "name": "NO_SUCH_ELEMENT", "type": {".class": "CallableType", "arg_kinds": [1, 1, 1], "arg_names": ["msg", "screen", "stacktrace"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": ["selenium.common.exceptions.NoSuchElementException"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "selenium.common.exceptions.NoSuchElementException", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "NO_SUCH_FRAME": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "selenium.webdriver.remote.errorhandler.ExceptionMapping.NO_SUCH_FRAME", "name": "NO_SUCH_FRAME", "type": {".class": "CallableType", "arg_kinds": [1, 1, 1], "arg_names": ["msg", "screen", "stacktrace"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": ["selenium.common.exceptions.NoSuchFrameException"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "selenium.common.exceptions.NoSuchFrameException", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "NO_SUCH_SHADOW_ROOT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "selenium.webdriver.remote.errorhandler.ExceptionMapping.NO_SUCH_SHADOW_ROOT", "name": "NO_SUCH_SHADOW_ROOT", "type": {".class": "CallableType", "arg_kinds": [1, 1, 1], "arg_names": ["msg", "screen", "stacktrace"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": ["selenium.common.exceptions.NoSuchShadowRootException"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "selenium.common.exceptions.NoSuchShadowRootException", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "NO_SUCH_WINDOW": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "selenium.webdriver.remote.errorhandler.ExceptionMapping.NO_SUCH_WINDOW", "name": "NO_SUCH_WINDOW", "type": {".class": "CallableType", "arg_kinds": [1, 1, 1], "arg_names": ["msg", "screen", "stacktrace"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": ["selenium.common.exceptions.NoSuchWindowException"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "selenium.common.exceptions.NoSuchWindowException", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "SCRIPT_TIMEOUT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "selenium.webdriver.remote.errorhandler.ExceptionMapping.SCRIPT_TIMEOUT", "name": "SCRIPT_TIMEOUT", "type": {".class": "CallableType", "arg_kinds": [1, 1, 1], "arg_names": ["msg", "screen", "stacktrace"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": ["selenium.common.exceptions.TimeoutException"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "selenium.common.exceptions.TimeoutException", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "SESSION_NOT_CREATED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "selenium.webdriver.remote.errorhandler.ExceptionMapping.SESSION_NOT_CREATED", "name": "SESSION_NOT_CREATED", "type": {".class": "CallableType", "arg_kinds": [1, 1, 1], "arg_names": ["msg", "screen", "stacktrace"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": ["selenium.common.exceptions.SessionNotCreatedException"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "selenium.common.exceptions.SessionNotCreatedException", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "STALE_ELEMENT_REFERENCE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "selenium.webdriver.remote.errorhandler.ExceptionMapping.STALE_ELEMENT_REFERENCE", "name": "STALE_ELEMENT_REFERENCE", "type": {".class": "CallableType", "arg_kinds": [1, 1, 1], "arg_names": ["msg", "screen", "stacktrace"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": ["selenium.common.exceptions.StaleElementReferenceException"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "selenium.common.exceptions.StaleElementReferenceException", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "TIMEOUT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "selenium.webdriver.remote.errorhandler.ExceptionMapping.TIMEOUT", "name": "TIMEOUT", "type": {".class": "CallableType", "arg_kinds": [1, 1, 1], "arg_names": ["msg", "screen", "stacktrace"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": ["selenium.common.exceptions.TimeoutException"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "selenium.common.exceptions.TimeoutException", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "UNABLE_TO_CAPTURE_SCREEN": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "selenium.webdriver.remote.errorhandler.ExceptionMapping.UNABLE_TO_CAPTURE_SCREEN", "name": "UNABLE_TO_CAPTURE_SCREEN", "type": {".class": "CallableType", "arg_kinds": [1, 1, 1], "arg_names": ["msg", "screen", "stacktrace"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": ["selenium.common.exceptions.ScreenshotException"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "selenium.common.exceptions.ScreenshotException", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "UNABLE_TO_SET_COOKIE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "selenium.webdriver.remote.errorhandler.ExceptionMapping.UNABLE_TO_SET_COOKIE", "name": "UNABLE_TO_SET_COOKIE", "type": {".class": "CallableType", "arg_kinds": [1, 1, 1], "arg_names": ["msg", "screen", "stacktrace"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": ["selenium.common.exceptions.UnableToSetCookieException"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "selenium.common.exceptions.UnableToSetCookieException", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "UNEXPECTED_ALERT_OPEN": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "selenium.webdriver.remote.errorhandler.ExceptionMapping.UNEXPECTED_ALERT_OPEN", "name": "UNEXPECTED_ALERT_OPEN", "type": {".class": "CallableType", "arg_kinds": [1, 1, 1, 1], "arg_names": ["msg", "screen", "stacktrace", "alert_text"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": ["selenium.common.exceptions.UnexpectedAlertPresentException"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "selenium.common.exceptions.UnexpectedAlertPresentException", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "UNKNOWN_ERROR": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "selenium.webdriver.remote.errorhandler.ExceptionMapping.UNKNOWN_ERROR", "name": "UNKNOWN_ERROR", "type": {".class": "CallableType", "arg_kinds": [1, 1, 1], "arg_names": ["msg", "screen", "stacktrace"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": ["selenium.common.exceptions.WebDriverException"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "selenium.common.exceptions.WebDriverException", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "UNKNOWN_METHOD": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "selenium.webdriver.remote.errorhandler.ExceptionMapping.UNKNOWN_METHOD", "name": "UNKNOWN_METHOD", "type": {".class": "CallableType", "arg_kinds": [1, 1, 1], "arg_names": ["msg", "screen", "stacktrace"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": ["selenium.common.exceptions.UnknownMethodException"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "selenium.common.exceptions.UnknownMethodException", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "selenium.webdriver.remote.errorhandler.ExceptionMapping.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "selenium.webdriver.remote.errorhandler.ExceptionMapping", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ImeActivationFailedException": {".class": "SymbolTableNode", "cross_ref": "selenium.common.exceptions.ImeActivationFailedException", "kind": "Gdef"}, "ImeNotAvailableException": {".class": "SymbolTableNode", "cross_ref": "selenium.common.exceptions.ImeNotAvailableException", "kind": "Gdef"}, "InsecureCertificateException": {".class": "SymbolTableNode", "cross_ref": "selenium.common.exceptions.InsecureCertificateException", "kind": "Gdef"}, "InvalidArgumentException": {".class": "SymbolTableNode", "cross_ref": "selenium.common.exceptions.InvalidArgumentException", "kind": "Gdef"}, "InvalidCookieDomainException": {".class": "SymbolTableNode", "cross_ref": "selenium.common.exceptions.InvalidCookieDomainException", "kind": "Gdef"}, "InvalidCoordinatesException": {".class": "SymbolTableNode", "cross_ref": "selenium.common.exceptions.InvalidCoordinatesException", "kind": "Gdef"}, "InvalidElementStateException": {".class": "SymbolTableNode", "cross_ref": "selenium.common.exceptions.InvalidElementStateException", "kind": "Gdef"}, "InvalidSelectorException": {".class": "SymbolTableNode", "cross_ref": "selenium.common.exceptions.InvalidSelectorException", "kind": "Gdef"}, "InvalidSessionIdException": {".class": "SymbolTableNode", "cross_ref": "selenium.common.exceptions.InvalidSessionIdException", "kind": "Gdef"}, "JavascriptException": {".class": "SymbolTableNode", "cross_ref": "selenium.common.exceptions.JavascriptException", "kind": "Gdef"}, "MoveTargetOutOfBoundsException": {".class": "SymbolTableNode", "cross_ref": "selenium.common.exceptions.MoveTargetOutOfBoundsException", "kind": "Gdef"}, "NoAlertPresentException": {".class": "SymbolTableNode", "cross_ref": "selenium.common.exceptions.NoAlertPresentException", "kind": "Gdef"}, "NoSuchCookieException": {".class": "SymbolTableNode", "cross_ref": "selenium.common.exceptions.NoSuchCookieException", "kind": "Gdef"}, "NoSuchElementException": {".class": "SymbolTableNode", "cross_ref": "selenium.common.exceptions.NoSuchElementException", "kind": "Gdef"}, "NoSuchFrameException": {".class": "SymbolTableNode", "cross_ref": "selenium.common.exceptions.NoSuchFrameException", "kind": "Gdef"}, "NoSuchShadowRootException": {".class": "SymbolTableNode", "cross_ref": "selenium.common.exceptions.NoSuchShadowRootException", "kind": "Gdef"}, "NoSuchWindowException": {".class": "SymbolTableNode", "cross_ref": "selenium.common.exceptions.NoSuchWindowException", "kind": "Gdef"}, "ScreenshotException": {".class": "SymbolTableNode", "cross_ref": "selenium.common.exceptions.ScreenshotException", "kind": "Gdef"}, "SessionNotCreatedException": {".class": "SymbolTableNode", "cross_ref": "selenium.common.exceptions.SessionNotCreatedException", "kind": "Gdef"}, "StaleElementReferenceException": {".class": "SymbolTableNode", "cross_ref": "selenium.common.exceptions.StaleElementReferenceException", "kind": "Gdef"}, "TimeoutException": {".class": "SymbolTableNode", "cross_ref": "selenium.common.exceptions.TimeoutException", "kind": "Gdef"}, "Type": {".class": "SymbolTableNode", "cross_ref": "typing.Type", "kind": "Gdef"}, "UnableToSetCookieException": {".class": "SymbolTableNode", "cross_ref": "selenium.common.exceptions.UnableToSetCookieException", "kind": "Gdef"}, "UnexpectedAlertPresentException": {".class": "SymbolTableNode", "cross_ref": "selenium.common.exceptions.UnexpectedAlertPresentException", "kind": "Gdef"}, "UnknownMethodException": {".class": "SymbolTableNode", "cross_ref": "selenium.common.exceptions.UnknownMethodException", "kind": "Gdef"}, "WebDriverException": {".class": "SymbolTableNode", "cross_ref": "selenium.common.exceptions.WebDriverException", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "selenium.webdriver.remote.errorhandler.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "selenium.webdriver.remote.errorhandler.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "selenium.webdriver.remote.errorhandler.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "selenium.webdriver.remote.errorhandler.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "selenium.webdriver.remote.errorhandler.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "selenium.webdriver.remote.errorhandler.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\selenium\\webdriver\\remote\\errorhandler.py"}