{".class": "MypyFile", "_fullname": "xml.dom.expatbuilder", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "CDATA_SECTION_NODE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "xml.dom.expatbuilder.CDATA_SECTION_NODE", "name": "CDATA_SECTION_NODE", "type": "builtins.int"}}, "DOCUMENT_NODE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "xml.dom.expatbuilder.DOCUMENT_NODE", "name": "DOCUMENT_NODE", "type": "builtins.int"}}, "DOMBuilderFilter": {".class": "SymbolTableNode", "cross_ref": "xml.dom.xmlbuilder.DOMBuilderFilter", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DOMImplementation": {".class": "SymbolTableNode", "cross_ref": "xml.dom.minidom.DOMImplementation", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Document": {".class": "SymbolTableNode", "cross_ref": "xml.dom.minidom.Document", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ElementInfo": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "xml.dom.expatbuilder.ElementInfo", "name": "ElementInfo", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "xml.dom.expatbuilder.ElementInfo", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "xml.dom.expatbuilder", "mro": ["xml.dom.expatbuilder.ElementInfo", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "tagName", "model"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "xml.dom.expatbuilder.ElementInfo.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "tagName", "model"], "arg_types": ["xml.dom.expatbuilder.ElementInfo", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.Incomplete"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ElementInfo", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getAttributeType": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "aname"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "xml.dom.expatbuilder.ElementInfo.getAttributeType", "name": "getAttributeType", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "aname"], "arg_types": ["xml.dom.expatbuilder.ElementInfo", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getAttributeType of ElementInfo", "ret_type": "xml.dom.minidom.TypeInfo", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getAttributeTypeNS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "namespaceURI", "localName"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "xml.dom.expatbuilder.ElementInfo.getAttributeTypeNS", "name": "getAttributeTypeNS", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "namespaceURI", "localName"], "arg_types": ["xml.dom.expatbuilder.ElementInfo", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getAttributeTypeNS of ElementInfo", "ret_type": "xml.dom.minidom.TypeInfo", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "isElementContent": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "xml.dom.expatbuilder.ElementInfo.isElementContent", "name": "is<PERSON>lement<PERSON><PERSON>nt", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["xml.dom.expatbuilder.ElementInfo"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "isElementContent of ElementInfo", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "isEmpty": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "xml.dom.expatbuilder.ElementInfo.isEmpty", "name": "isEmpty", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["xml.dom.expatbuilder.ElementInfo"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "isEmpty of ElementInfo", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "isId": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "aname"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "xml.dom.expatbuilder.ElementInfo.isId", "name": "isId", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "aname"], "arg_types": ["xml.dom.expatbuilder.ElementInfo", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "isId of ElementInfo", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "isIdNS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "euri", "ename", "auri", "aname"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "xml.dom.expatbuilder.ElementInfo.isIdNS", "name": "isIdNS", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "euri", "ename", "auri", "aname"], "arg_types": ["xml.dom.expatbuilder.ElementInfo", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "isIdNS of ElementInfo", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "tagName": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "xml.dom.expatbuilder.ElementInfo.tagName", "name": "tagName", "type": {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.Incomplete"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "xml.dom.expatbuilder.ElementInfo.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "xml.dom.expatbuilder.ElementInfo", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ExpatBuilder": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "xml.dom.expatbuilder.ExpatBuilder", "name": "ExpatBuilder", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "xml.dom.expatbuilder.ExpatBuilder", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "xml.dom.expatbuilder", "mro": ["xml.dom.expatbuilder.ExpatBuilder", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "options"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "xml.dom.expatbuilder.ExpatBuilder.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "options"], "arg_types": ["xml.dom.expatbuilder.ExpatBuilder", {".class": "UnionType", "items": ["xml.dom.xmlbuilder.Options", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ExpatBuilder", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "attlist_decl_handler": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "elem", "name", "type", "default", "required"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "xml.dom.expatbuilder.ExpatBuilder.attlist_decl_handler", "name": "attlist_decl_handler", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "elem", "name", "type", "default", "required"], "arg_types": ["xml.dom.expatbuilder.ExpatBuilder", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "attlist_decl_handler of ExpatBuilder", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "character_data_handler": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "xml.dom.expatbuilder.ExpatBuilder.character_data_handler", "name": "character_data_handler", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "data"], "arg_types": ["xml.dom.expatbuilder.ExpatBuilder", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "character_data_handler of ExpatBuilder", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "character_data_handler_cdata": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "xml.dom.expatbuilder.ExpatBuilder.character_data_handler_cdata", "name": "character_data_handler_cdata", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "data"], "arg_types": ["xml.dom.expatbuilder.ExpatBuilder", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "character_data_handler_cdata of ExpatBuilder", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "comment_handler": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "xml.dom.expatbuilder.ExpatBuilder.comment_handler", "name": "comment_handler", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "data"], "arg_types": ["xml.dom.expatbuilder.ExpatBuilder", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "comment_handler of ExpatBuilder", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "createParser": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "xml.dom.expatbuilder.ExpatBuilder.createParser", "name": "create<PERSON><PERSON><PERSON>", "type": null}}, "curNode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "xml.dom.expatbuilder.ExpatBuilder.curNode", "name": "curNode", "type": {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.Incomplete"}}}, "document": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "xml.dom.expatbuilder.ExpatBuilder.document", "name": "document", "type": "xml.dom.minidom.Document"}}, "element_decl_handler": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "model"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "xml.dom.expatbuilder.ExpatBuilder.element_decl_handler", "name": "element_decl_handler", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "model"], "arg_types": ["xml.dom.expatbuilder.ExpatBuilder", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "element_decl_handler of ExpatBuilder", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "end_cdata_section_handler": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "xml.dom.expatbuilder.ExpatBuilder.end_cdata_section_handler", "name": "end_cdata_section_handler", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["xml.dom.expatbuilder.ExpatBuilder"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "end_cdata_section_handler of ExpatBuilder", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "end_doctype_decl_handler": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "xml.dom.expatbuilder.ExpatBuilder.end_doctype_decl_handler", "name": "end_doctype_decl_handler", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["xml.dom.expatbuilder.ExpatBuilder"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "end_doctype_decl_handler of ExpatBuilder", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "end_element_handler": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "xml.dom.expatbuilder.ExpatBuilder.end_element_handler", "name": "end_element_handler", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "name"], "arg_types": ["xml.dom.expatbuilder.ExpatBuilder", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "end_element_handler of ExpatBuilder", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "entity_decl_handler": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "entityName", "is_parameter_entity", "value", "base", "systemId", "publicId", "notationName"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "xml.dom.expatbuilder.ExpatBuilder.entity_decl_handler", "name": "entity_decl_handler", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "entityName", "is_parameter_entity", "value", "base", "systemId", "publicId", "notationName"], "arg_types": ["xml.dom.expatbuilder.ExpatBuilder", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "entity_decl_handler of ExpatBuilder", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "external_entity_ref_handler": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "context", "base", "systemId", "publicId"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "xml.dom.expatbuilder.ExpatBuilder.external_entity_ref_handler", "name": "external_entity_ref_handler", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "context", "base", "systemId", "publicId"], "arg_types": ["xml.dom.expatbuilder.ExpatBuilder", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "external_entity_ref_handler of ExpatBuilder", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "first_element_handler": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "attributes"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "xml.dom.expatbuilder.ExpatBuilder.first_element_handler", "name": "first_element_handler", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "attributes"], "arg_types": ["xml.dom.expatbuilder.ExpatBuilder", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "first_element_handler of ExpatBuilder", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getParser": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "xml.dom.expatbuilder.ExpatBuilder.getParser", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": null}}, "install": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "parser"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "xml.dom.expatbuilder.ExpatBuilder.install", "name": "install", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "parser"], "arg_types": ["xml.dom.expatbuilder.ExpatBuilder", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "install of ExpatBuilder", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "notation_decl_handler": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "notationName", "base", "systemId", "publicId"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "xml.dom.expatbuilder.ExpatBuilder.notation_decl_handler", "name": "notation_decl_handler", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "notationName", "base", "systemId", "publicId"], "arg_types": ["xml.dom.expatbuilder.ExpatBuilder", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "notation_decl_handler of ExpatBuilder", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parseFile": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "file"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "xml.dom.expatbuilder.ExpatBuilder.parseFile", "name": "parseFile", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "file"], "arg_types": ["xml.dom.expatbuilder.ExpatBuilder", {".class": "Instance", "args": [{".class": "UnionType", "items": ["_collections_abc.<PERSON><PERSON>er", "builtins.str"], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "_typeshed.SupportsRead"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parseFile of ExpatBuilder", "ret_type": "xml.dom.minidom.Document", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parseString": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "string"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "xml.dom.expatbuilder.ExpatBuilder.parseString", "name": "parseString", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "string"], "arg_types": ["xml.dom.expatbuilder.ExpatBuilder", {".class": "UnionType", "items": ["builtins.str", "_collections_abc.<PERSON><PERSON>er"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parseString of ExpatBuilder", "ret_type": "xml.dom.minidom.Document", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pi_handler": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "target", "data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "xml.dom.expatbuilder.ExpatBuilder.pi_handler", "name": "pi_handler", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "target", "data"], "arg_types": ["xml.dom.expatbuilder.ExpatBuilder", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pi_handler of ExpatBuilder", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "reset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "xml.dom.expatbuilder.ExpatBuilder.reset", "name": "reset", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["xml.dom.expatbuilder.ExpatBuilder"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reset of ExpatBuilder", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "start_cdata_section_handler": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "xml.dom.expatbuilder.ExpatBuilder.start_cdata_section_handler", "name": "start_cdata_section_handler", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["xml.dom.expatbuilder.ExpatBuilder"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "start_cdata_section_handler of ExpatBuilder", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "start_doctype_decl_handler": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "doctypeName", "systemId", "publicId", "has_internal_subset"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "xml.dom.expatbuilder.ExpatBuilder.start_doctype_decl_handler", "name": "start_doctype_decl_handler", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "doctypeName", "systemId", "publicId", "has_internal_subset"], "arg_types": ["xml.dom.expatbuilder.ExpatBuilder", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "start_doctype_decl_handler of ExpatBuilder", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "start_element_handler": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "attributes"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "xml.dom.expatbuilder.ExpatBuilder.start_element_handler", "name": "start_element_handler", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "attributes"], "arg_types": ["xml.dom.expatbuilder.ExpatBuilder", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "start_element_handler of ExpatBuilder", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "xml_decl_handler": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "version", "encoding", "standalone"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "xml.dom.expatbuilder.ExpatBuilder.xml_decl_handler", "name": "xml_decl_handler", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "version", "encoding", "standalone"], "arg_types": ["xml.dom.expatbuilder.ExpatBuilder", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "xml_decl_handler of ExpatBuilder", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "xml.dom.expatbuilder.ExpatBuilder.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "xml.dom.expatbuilder.ExpatBuilder", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ExpatBuilderNS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["xml.dom.expatbuilder.Namespaces", "xml.dom.expatbuilder.ExpatBuilder"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "xml.dom.expatbuilder.ExpatBuilderNS", "name": "ExpatBuilderNS", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "xml.dom.expatbuilder.ExpatBuilderNS", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "xml.dom.expatbuilder", "mro": ["xml.dom.expatbuilder.ExpatBuilderNS", "xml.dom.expatbuilder.Namespaces", "xml.dom.expatbuilder.ExpatBuilder", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "xml.dom.expatbuilder.ExpatBuilderNS.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "xml.dom.expatbuilder.ExpatBuilderNS", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FILTER_ACCEPT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "xml.dom.expatbuilder.FILTER_ACCEPT", "name": "FILTER_ACCEPT", "type": {".class": "LiteralType", "fallback": "builtins.int", "value": 1}}}, "FILTER_INTERRUPT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "xml.dom.expatbuilder.FILTER_INTERRUPT", "name": "FILTER_INTERRUPT", "type": {".class": "LiteralType", "fallback": "builtins.int", "value": 4}}}, "FILTER_REJECT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "xml.dom.expatbuilder.FILTER_REJECT", "name": "FILTER_REJECT", "type": {".class": "LiteralType", "fallback": "builtins.int", "value": 2}}}, "FILTER_SKIP": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "xml.dom.expatbuilder.FILTER_SKIP", "name": "FILTER_SKIP", "type": {".class": "LiteralType", "fallback": "builtins.int", "value": 3}}}, "FilterCrutch": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "xml.dom.expatbuilder.FilterCrutch", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "xml.dom.expatbuilder.FilterCrutch", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "xml.dom.expatbuilder", "mro": ["xml.dom.expatbuilder.FilterCrutch", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "builder"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "xml.dom.expatbuilder.FilterCrutch.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "builder"], "arg_types": ["xml.dom.expatbuilder.FilterCrutch", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of FilterCrutch", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "xml.dom.expatbuilder.FilterCrutch.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "xml.dom.expatbuilder.FilterCrutch", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FilterVisibilityController": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "xml.dom.expatbuilder.FilterVisibilityController", "name": "FilterVisibilityController", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "xml.dom.expatbuilder.FilterVisibilityController", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "xml.dom.expatbuilder", "mro": ["xml.dom.expatbuilder.FilterVisibilityController", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "filter"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "xml.dom.expatbuilder.FilterVisibilityController.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "filter"], "arg_types": ["xml.dom.expatbuilder.FilterVisibilityController", "xml.dom.xmlbuilder.DOMBuilderFilter"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of FilterVisibilityController", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "acceptNode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "xml.dom.expatbuilder.FilterVisibilityController.acceptNode", "name": "acceptNode", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["xml.dom.expatbuilder.FilterVisibilityController", "xml.dom.minidom.Node"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "acceptNode of FilterVisibilityController", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "filter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "xml.dom.expatbuilder.FilterVisibilityController.filter", "name": "filter", "type": "xml.dom.xmlbuilder.DOMBuilderFilter"}}, "startContainer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "xml.dom.expatbuilder.FilterVisibilityController.startContainer", "name": "startContainer", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["xml.dom.expatbuilder.FilterVisibilityController", "xml.dom.minidom.Node"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "startContainer of FilterVisibilityController", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "xml.dom.expatbuilder.FilterVisibilityController.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "xml.dom.expatbuilder.FilterVisibilityController", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FragmentBuilder": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["xml.dom.expatbuilder.ExpatBuilder"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "xml.dom.expatbuilder.FragmentBuilder", "name": "FragmentBuilder", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "xml.dom.expatbuilder.FragmentBuilder", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "xml.dom.expatbuilder", "mro": ["xml.dom.expatbuilder.FragmentBuilder", "xml.dom.expatbuilder.ExpatBuilder", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "context", "options"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "xml.dom.expatbuilder.FragmentBuilder.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "context", "options"], "arg_types": ["xml.dom.expatbuilder.FragmentBuilder", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "UnionType", "items": ["xml.dom.xmlbuilder.Options", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of FragmentBuilder", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "context": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "xml.dom.expatbuilder.FragmentBuilder.context", "name": "context", "type": {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.Incomplete"}}}, "fragment": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "xml.dom.expatbuilder.FragmentBuilder.fragment", "name": "fragment", "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.Incomplete"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "originalDocument": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "xml.dom.expatbuilder.FragmentBuilder.originalDocument", "name": "originalDocument", "type": {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.Incomplete"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "xml.dom.expatbuilder.FragmentBuilder.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "xml.dom.expatbuilder.FragmentBuilder", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FragmentBuilderNS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["xml.dom.expatbuilder.Namespaces", "xml.dom.expatbuilder.FragmentBuilder"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "xml.dom.expatbuilder.FragmentBuilderNS", "name": "FragmentBuilderNS", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "xml.dom.expatbuilder.FragmentBuilderNS", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "xml.dom.expatbuilder", "mro": ["xml.dom.expatbuilder.FragmentBuilderNS", "xml.dom.expatbuilder.Namespaces", "xml.dom.expatbuilder.FragmentBuilder", "xml.dom.expatbuilder.ExpatBuilder", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "xml.dom.expatbuilder.FragmentBuilderNS.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "xml.dom.expatbuilder.FragmentBuilderNS", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Incomplete": {".class": "SymbolTableNode", "cross_ref": "_typeshed.Incomplete", "kind": "Gdef", "module_hidden": true, "module_public": false}, "InternalSubsetExtractor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["xml.dom.expatbuilder.ExpatBuilder"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "xml.dom.expatbuilder.InternalSubsetExtractor", "name": "InternalSubsetExtractor", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "xml.dom.expatbuilder.InternalSubsetExtractor", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "xml.dom.expatbuilder", "mro": ["xml.dom.expatbuilder.InternalSubsetExtractor", "xml.dom.expatbuilder.ExpatBuilder", "builtins.object"], "names": {".class": "SymbolTable", "end_doctype_decl_handler": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "xml.dom.expatbuilder.InternalSubsetExtractor.end_doctype_decl_handler", "name": "end_doctype_decl_handler", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["xml.dom.expatbuilder.InternalSubsetExtractor"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "end_doctype_decl_handler of InternalSubsetExtractor", "ret_type": {".class": "UninhabitedType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "getSubset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "xml.dom.expatbuilder.InternalSubsetExtractor.getSubset", "name": "getSubset", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["xml.dom.expatbuilder.InternalSubsetExtractor"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getSubset of InternalSubsetExtractor", "ret_type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parseFile": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "file"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "xml.dom.expatbuilder.InternalSubsetExtractor.parseFile", "name": "parseFile", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "file"], "arg_types": ["xml.dom.expatbuilder.InternalSubsetExtractor", {".class": "Instance", "args": [{".class": "UnionType", "items": ["_collections_abc.<PERSON><PERSON>er", "builtins.str"], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "_typeshed.SupportsRead"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parseFile of InternalSubsetExtractor", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parseString": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "string"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "xml.dom.expatbuilder.InternalSubsetExtractor.parseString", "name": "parseString", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "string"], "arg_types": ["xml.dom.expatbuilder.InternalSubsetExtractor", {".class": "UnionType", "items": ["builtins.str", "_collections_abc.<PERSON><PERSON>er"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parseString of InternalSubsetExtractor", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "start_doctype_decl_handler": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "name", "publicId", "systemId", "has_internal_subset"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "xml.dom.expatbuilder.InternalSubsetExtractor.start_doctype_decl_handler", "name": "start_doctype_decl_handler", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "name", "publicId", "systemId", "has_internal_subset"], "arg_types": ["xml.dom.expatbuilder.InternalSubsetExtractor", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "start_doctype_decl_handler of InternalSubsetExtractor", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "start_element_handler": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "attrs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "xml.dom.expatbuilder.InternalSubsetExtractor.start_element_handler", "name": "start_element_handler", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "attrs"], "arg_types": ["xml.dom.expatbuilder.InternalSubsetExtractor", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "start_element_handler of InternalSubsetExtractor", "ret_type": {".class": "UninhabitedType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "subset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "xml.dom.expatbuilder.InternalSubsetExtractor.subset", "name": "subset", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "xml.dom.expatbuilder.InternalSubsetExtractor.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "xml.dom.expatbuilder.InternalSubsetExtractor", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Namespaces": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "xml.dom.expatbuilder.Namespaces", "name": "Namespaces", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "xml.dom.expatbuilder.Namespaces", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "xml.dom.expatbuilder", "mro": ["xml.dom.expatbuilder.Namespaces", "builtins.object"], "names": {".class": "SymbolTable", "createParser": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "xml.dom.expatbuilder.Namespaces.createParser", "name": "create<PERSON><PERSON><PERSON>", "type": null}}, "end_element_handler": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "xml.dom.expatbuilder.Namespaces.end_element_handler", "name": "end_element_handler", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "name"], "arg_types": ["xml.dom.expatbuilder.Namespaces", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "end_element_handler of Namespaces", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "install": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "parser"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "xml.dom.expatbuilder.Namespaces.install", "name": "install", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "parser"], "arg_types": ["xml.dom.expatbuilder.Namespaces", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "install of Namespaces", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "start_element_handler": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "attributes"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "xml.dom.expatbuilder.Namespaces.start_element_handler", "name": "start_element_handler", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "attributes"], "arg_types": ["xml.dom.expatbuilder.Namespaces", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "start_element_handler of Namespaces", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "start_namespace_decl_handler": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "prefix", "uri"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "xml.dom.expatbuilder.Namespaces.start_namespace_decl_handler", "name": "start_namespace_decl_handler", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "prefix", "uri"], "arg_types": ["xml.dom.expatbuilder.Namespaces", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "start_namespace_decl_handler of Namespaces", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "xml.dom.expatbuilder.Namespaces.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "xml.dom.expatbuilder.Namespaces", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NoReturn": {".class": "SymbolTableNode", "cross_ref": "typing.NoReturn", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Node": {".class": "SymbolTableNode", "cross_ref": "xml.dom.minidom.Node", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Options": {".class": "SymbolTableNode", "cross_ref": "xml.dom.xmlbuilder.Options", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ParseEscape": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.Exception"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "xml.dom.expatbuilder.ParseEscape", "name": "ParseEscape", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "xml.dom.expatbuilder.ParseEscape", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "xml.dom.expatbuilder", "mro": ["xml.dom.expatbuilder.ParseEscape", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "xml.dom.expatbuilder.ParseEscape.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "xml.dom.expatbuilder.ParseEscape", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ReadableBuffer": {".class": "SymbolTableNode", "cross_ref": "_typeshed.ReadableBuffer", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Rejecter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["xml.dom.expatbuilder.FilterCrutch"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "xml.dom.expatbuilder.Rejecter", "name": "Rejecter", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "xml.dom.expatbuilder.Rejecter", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "xml.dom.expatbuilder", "mro": ["xml.dom.expatbuilder.Rejecter", "xml.dom.expatbuilder.FilterCrutch", "builtins.object"], "names": {".class": "SymbolTable", "end_element_handler": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": ["self", "args"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "xml.dom.expatbuilder.Rejecter.end_element_handler", "name": "end_element_handler", "type": {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": ["self", "args"], "arg_types": ["xml.dom.expatbuilder.Rejecter", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "end_element_handler of Rejecter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "start_element_handler": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": ["self", "args"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "xml.dom.expatbuilder.Rejecter.start_element_handler", "name": "start_element_handler", "type": {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": ["self", "args"], "arg_types": ["xml.dom.expatbuilder.Rejecter", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "start_element_handler of Rejecter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "xml.dom.expatbuilder.Rejecter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "xml.dom.expatbuilder.Rejecter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Skipper": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["xml.dom.expatbuilder.FilterCrutch"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "xml.dom.expatbuilder.Skipper", "name": "Skipper", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "xml.dom.expatbuilder.Skipper", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "xml.dom.expatbuilder", "mro": ["xml.dom.expatbuilder.Skipper", "xml.dom.expatbuilder.FilterCrutch", "builtins.object"], "names": {".class": "SymbolTable", "end_element_handler": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": ["self", "args"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "xml.dom.expatbuilder.Skipper.end_element_handler", "name": "end_element_handler", "type": {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": ["self", "args"], "arg_types": ["xml.dom.expatbuilder.Skipper", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "end_element_handler of <PERSON>pper", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "start_element_handler": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": ["self", "args"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "xml.dom.expatbuilder.Skipper.start_element_handler", "name": "start_element_handler", "type": {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": ["self", "args"], "arg_types": ["xml.dom.expatbuilder.Skipper", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "start_element_handler of Skipper", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "xml.dom.expatbuilder.Skipper.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "xml.dom.expatbuilder.Skipper", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SupportsRead": {".class": "SymbolTableNode", "cross_ref": "_typeshed.SupportsRead", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TEXT_NODE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "xml.dom.expatbuilder.TEXT_NODE", "name": "TEXT_NODE", "type": "builtins.int"}}, "TypeInfo": {".class": "SymbolTableNode", "cross_ref": "xml.dom.minidom.TypeInfo", "kind": "Gdef", "module_hidden": true, "module_public": false}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "xml.dom.expatbuilder.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "xml.dom.expatbuilder.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "xml.dom.expatbuilder.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "xml.dom.expatbuilder.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "xml.dom.expatbuilder.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "xml.dom.expatbuilder.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "makeBuilder": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["options"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "xml.dom.expatbuilder.makeBuilder", "name": "makeBuilder", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["options"], "arg_types": ["xml.dom.xmlbuilder.Options"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "makeBuilder", "ret_type": {".class": "UnionType", "items": ["xml.dom.expatbuilder.ExpatBuilderNS", "xml.dom.expatbuilder.ExpatBuilder"], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parse": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["file", "namespaces"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "xml.dom.expatbuilder.parse", "name": "parse", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["file", "namespaces"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": [{".class": "UnionType", "items": ["_collections_abc.<PERSON><PERSON>er", "builtins.str"], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "_typeshed.SupportsRead"}], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parseFragment": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["file", "context", "namespaces"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "xml.dom.expatbuilder.parseFragment", "name": "parseFragment", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["file", "context", "namespaces"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parseFragment", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parseFragmentString": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["string", "context", "namespaces"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "xml.dom.expatbuilder.parseFragmentString", "name": "parseFragmentString", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["string", "context", "namespaces"], "arg_types": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parseFragmentString", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parseString": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["string", "namespaces"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "xml.dom.expatbuilder.parseString", "name": "parseString", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["string", "namespaces"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", "_collections_abc.<PERSON><PERSON>er"], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parseString", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "theDOMImplementation": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "xml.dom.expatbuilder.theDOMImplementation", "name": "theDOMImplementation", "type": {".class": "UnionType", "items": ["xml.dom.minidom.DOMImplementation", {".class": "NoneType"}], "uses_pep604_syntax": true}}}}, "path": "c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.mypy-type-checker-2025.2.0\\bundled\\libs\\mypy\\typeshed\\stdlib\\xml\\dom\\expatbuilder.pyi"}