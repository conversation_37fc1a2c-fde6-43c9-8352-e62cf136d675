{".class": "MypyFile", "_fullname": "webdriver_manager.chrome", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ChromeDriver": {".class": "SymbolTableNode", "cross_ref": "webdriver_manager.drivers.chrome.ChromeDriver", "kind": "Gdef"}, "ChromeDriverManager": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["webdriver_manager.core.manager.DriverManager"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "webdriver_manager.chrome.ChromeDriverManager", "name": "ChromeDriverManager", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "webdriver_manager.chrome.ChromeDriverManager", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "webdriver_manager.chrome", "mro": ["webdriver_manager.chrome.ChromeDriverManager", "webdriver_manager.core.manager.DriverManager", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "driver_version", "name", "url", "latest_release_url", "chrome_type", "download_manager", "cache_manager", "os_system_manager"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "webdriver_manager.chrome.ChromeDriverManager.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "driver_version", "name", "url", "latest_release_url", "chrome_type", "download_manager", "cache_manager", "os_system_manager"], "arg_types": ["webdriver_manager.chrome.ChromeDriverManager", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", "builtins.str", "builtins.str", "builtins.str", {".class": "UnionType", "items": ["webdriver_manager.core.download_manager.DownloadManager", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["webdriver_manager.core.driver_cache.DriverCacheManager", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["webdriver_manager.core.os_manager.OperationSystemManager", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ChromeDriverManager", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "driver": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "webdriver_manager.chrome.ChromeDriverManager.driver", "name": "driver", "type": "webdriver_manager.drivers.chrome.ChromeDriver"}}, "get_os_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "webdriver_manager.chrome.ChromeDriverManager.get_os_type", "name": "get_os_type", "type": null}}, "install": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "webdriver_manager.chrome.ChromeDriverManager.install", "name": "install", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["webdriver_manager.chrome.ChromeDriverManager"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "install of ChromeDriverManager", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "webdriver_manager.chrome.ChromeDriverManager.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "webdriver_manager.chrome.ChromeDriverManager", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ChromeType": {".class": "SymbolTableNode", "cross_ref": "webdriver_manager.core.os_manager.ChromeType", "kind": "Gdef"}, "DownloadManager": {".class": "SymbolTableNode", "cross_ref": "webdriver_manager.core.download_manager.DownloadManager", "kind": "Gdef"}, "DriverCacheManager": {".class": "SymbolTableNode", "cross_ref": "webdriver_manager.core.driver_cache.DriverCacheManager", "kind": "Gdef"}, "DriverManager": {".class": "SymbolTableNode", "cross_ref": "webdriver_manager.core.manager.DriverManager", "kind": "Gdef"}, "OperationSystemManager": {".class": "SymbolTableNode", "cross_ref": "webdriver_manager.core.os_manager.OperationSystemManager", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "webdriver_manager.chrome.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "webdriver_manager.chrome.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "webdriver_manager.chrome.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "webdriver_manager.chrome.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "webdriver_manager.chrome.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "webdriver_manager.chrome.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\webdriver_manager\\chrome.py"}