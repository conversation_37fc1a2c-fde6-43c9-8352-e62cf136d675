{".class": "MypyFile", "_fullname": "selenium.webdriver.common.actions.pointer_actions", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Interaction": {".class": "SymbolTableNode", "cross_ref": "selenium.webdriver.common.actions.interaction.Interaction", "kind": "Gdef"}, "MouseButton": {".class": "SymbolTableNode", "cross_ref": "selenium.webdriver.common.actions.mouse_button.MouseButton", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "PointerActions": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["selenium.webdriver.common.actions.interaction.Interaction"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "selenium.webdriver.common.actions.pointer_actions.PointerActions", "name": "PointerActions", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "selenium.webdriver.common.actions.pointer_actions.PointerActions", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "selenium.webdriver.common.actions.pointer_actions", "mro": ["selenium.webdriver.common.actions.pointer_actions.PointerActions", "selenium.webdriver.common.actions.interaction.Interaction", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "source", "duration"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "selenium.webdriver.common.actions.pointer_actions.PointerActions.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "source", "duration"], "arg_types": ["selenium.webdriver.common.actions.pointer_actions.PointerActions", {".class": "UnionType", "items": ["selenium.webdriver.common.actions.pointer_input.PointerInput", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of PointerActions", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_button_action": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "action", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "selenium.webdriver.common.actions.pointer_actions.PointerActions._button_action", "name": "_button_action", "type": null}}, "_duration": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "selenium.webdriver.common.actions.pointer_actions.PointerActions._duration", "name": "_duration", "type": "builtins.int"}}, "click": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "element", "button"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "selenium.webdriver.common.actions.pointer_actions.PointerActions.click", "name": "click", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "element", "button"], "arg_types": ["selenium.webdriver.common.actions.pointer_actions.PointerActions", {".class": "UnionType", "items": ["selenium.webdriver.remote.webelement.WebElement", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "click of PointerActions", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "click_and_hold": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "element", "button"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "selenium.webdriver.common.actions.pointer_actions.PointerActions.click_and_hold", "name": "click_and_hold", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "element", "button"], "arg_types": ["selenium.webdriver.common.actions.pointer_actions.PointerActions", {".class": "UnionType", "items": ["selenium.webdriver.remote.webelement.WebElement", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "click_and_hold of PointerActions", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "context_click": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "element"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "selenium.webdriver.common.actions.pointer_actions.PointerActions.context_click", "name": "context_click", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "element"], "arg_types": ["selenium.webdriver.common.actions.pointer_actions.PointerActions", {".class": "UnionType", "items": ["selenium.webdriver.remote.webelement.WebElement", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "context_click of PointerActions", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "double_click": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "element"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "selenium.webdriver.common.actions.pointer_actions.PointerActions.double_click", "name": "double_click", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "element"], "arg_types": ["selenium.webdriver.common.actions.pointer_actions.PointerActions", {".class": "UnionType", "items": ["selenium.webdriver.remote.webelement.WebElement", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "double_click of PointerActions", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "move_by": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "x", "y", "width", "height", "pressure", "tangential_pressure", "tilt_x", "tilt_y", "twist", "altitude_angle", "azimuth_angle"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "selenium.webdriver.common.actions.pointer_actions.PointerActions.move_by", "name": "move_by", "type": null}}, "move_to": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "element", "x", "y", "width", "height", "pressure", "tangential_pressure", "tilt_x", "tilt_y", "twist", "altitude_angle", "azimuth_angle"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "selenium.webdriver.common.actions.pointer_actions.PointerActions.move_to", "name": "move_to", "type": null}}, "move_to_location": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "x", "y", "width", "height", "pressure", "tangential_pressure", "tilt_x", "tilt_y", "twist", "altitude_angle", "azimuth_angle"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "selenium.webdriver.common.actions.pointer_actions.PointerActions.move_to_location", "name": "move_to_location", "type": null}}, "pause": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "duration"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "selenium.webdriver.common.actions.pointer_actions.PointerActions.pause", "name": "pause", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "duration"], "arg_types": ["selenium.webdriver.common.actions.pointer_actions.PointerActions", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pause of PointerActions", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pointer_down": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "button", "width", "height", "pressure", "tangential_pressure", "tilt_x", "tilt_y", "twist", "altitude_angle", "azimuth_angle"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "selenium.webdriver.common.actions.pointer_actions.PointerActions.pointer_down", "name": "pointer_down", "type": null}}, "pointer_up": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "button"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "selenium.webdriver.common.actions.pointer_actions.PointerActions.pointer_up", "name": "pointer_up", "type": null}}, "release": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "button"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "selenium.webdriver.common.actions.pointer_actions.PointerActions.release", "name": "release", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "selenium.webdriver.common.actions.pointer_actions.PointerActions.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "selenium.webdriver.common.actions.pointer_actions.PointerActions", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PointerInput": {".class": "SymbolTableNode", "cross_ref": "selenium.webdriver.common.actions.pointer_input.PointerInput", "kind": "Gdef"}, "WebElement": {".class": "SymbolTableNode", "cross_ref": "selenium.webdriver.remote.webelement.WebElement", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "selenium.webdriver.common.actions.pointer_actions.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "selenium.webdriver.common.actions.pointer_actions.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "selenium.webdriver.common.actions.pointer_actions.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "selenium.webdriver.common.actions.pointer_actions.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "selenium.webdriver.common.actions.pointer_actions.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "selenium.webdriver.common.actions.pointer_actions.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "interaction": {".class": "SymbolTableNode", "cross_ref": "selenium.webdriver.common.actions.interaction", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\selenium\\webdriver\\common\\actions\\pointer_actions.py"}