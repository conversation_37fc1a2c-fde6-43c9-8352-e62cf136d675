{".class": "MypyFile", "_fullname": "selenium.webdriver.chrome.webdriver", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ChromiumDriver": {".class": "SymbolTableNode", "cross_ref": "selenium.webdriver.chromium.webdriver.ChromiumDriver", "kind": "Gdef"}, "DesiredCapabilities": {".class": "SymbolTableNode", "cross_ref": "selenium.webdriver.common.desired_capabilities.DesiredCapabilities", "kind": "Gdef"}, "Options": {".class": "SymbolTableNode", "cross_ref": "selenium.webdriver.chrome.options.Options", "kind": "Gdef"}, "Service": {".class": "SymbolTableNode", "cross_ref": "selenium.webdriver.chrome.service.Service", "kind": "Gdef"}, "WebDriver": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["selenium.webdriver.chromium.webdriver.ChromiumDriver"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "selenium.webdriver.chrome.webdriver.WebDriver", "name": "WebDriver", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "selenium.webdriver.chrome.webdriver.WebDriver", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "selenium.webdriver.chrome.webdriver", "mro": ["selenium.webdriver.chrome.webdriver.WebDriver", "selenium.webdriver.chromium.webdriver.ChromiumDriver", "selenium.webdriver.remote.webdriver.WebDriver", "selenium.webdriver.remote.webdriver.BaseWebDriver", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "options", "service", "keep_alive"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "selenium.webdriver.chrome.webdriver.WebDriver.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "options", "service", "keep_alive"], "arg_types": ["selenium.webdriver.chrome.webdriver.WebDriver", "selenium.webdriver.chrome.options.Options", "selenium.webdriver.chrome.service.Service", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of WebDriver", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "selenium.webdriver.chrome.webdriver.WebDriver.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "selenium.webdriver.chrome.webdriver.WebDriver", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "selenium.webdriver.chrome.webdriver.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "selenium.webdriver.chrome.webdriver.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "selenium.webdriver.chrome.webdriver.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "selenium.webdriver.chrome.webdriver.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "selenium.webdriver.chrome.webdriver.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "selenium.webdriver.chrome.webdriver.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\selenium\\webdriver\\chrome\\webdriver.py"}