{".class": "MypyFile", "_fullname": "selenium.webdriver.firefox.firefox_binary", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "FirefoxBinary": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "selenium.webdriver.firefox.firefox_binary.FirefoxBinary", "name": "FirefoxBinary", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "selenium.webdriver.firefox.firefox_binary.FirefoxBinary", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "selenium.webdriver.firefox.firefox_binary", "mro": ["selenium.webdriver.firefox.firefox_binary.FirefoxBinary", "builtins.object"], "names": {".class": "SymbolTable", "NO_FOCUS_LIBRARY_NAME": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "selenium.webdriver.firefox.firefox_binary.FirefoxBinary.NO_FOCUS_LIBRARY_NAME", "name": "NO_FOCUS_LIBRARY_NAME", "type": "builtins.str"}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "firefox_path", "log_file"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "selenium.webdriver.firefox.firefox_binary.FirefoxBinary.__init__", "name": "__init__", "type": null}}, "_default_windows_location": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "selenium.webdriver.firefox.firefox_binary.FirefoxBinary._default_windows_location", "name": "_default_windows_location", "type": null}}, "_extract_and_check": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "profile", "x86", "amd64"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "selenium.webdriver.firefox.firefox_binary.FirefoxBinary._extract_and_check", "name": "_extract_and_check", "type": null}}, "_find_exe_in_registry": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "selenium.webdriver.firefox.firefox_binary.FirefoxBinary._find_exe_in_registry", "name": "_find_exe_in_registry", "type": null}}, "_firefox_env": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "selenium.webdriver.firefox.firefox_binary.FirefoxBinary._firefox_env", "name": "_firefox_env", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_get_firefox_start_cmd": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "selenium.webdriver.firefox.firefox_binary.FirefoxBinary._get_firefox_start_cmd", "name": "_get_firefox_start_cmd", "type": null}}, "_log_file": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "selenium.webdriver.firefox.firefox_binary.FirefoxBinary._log_file", "name": "_log_file", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_modify_link_library_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "selenium.webdriver.firefox.firefox_binary.FirefoxBinary._modify_link_library_path", "name": "_modify_link_library_path", "type": null}}, "_start_cmd": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "selenium.webdriver.firefox.firefox_binary.FirefoxBinary._start_cmd", "name": "_start_cmd", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_start_from_profile_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "selenium.webdriver.firefox.firefox_binary.FirefoxBinary._start_from_profile_path", "name": "_start_from_profile_path", "type": null}}, "_wait_until_connectable": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "timeout"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "selenium.webdriver.firefox.firefox_binary.FirefoxBinary._wait_until_connectable", "name": "_wait_until_connectable", "type": null}}, "add_command_line_options": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": ["self", "args"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "selenium.webdriver.firefox.firefox_binary.FirefoxBinary.add_command_line_options", "name": "add_command_line_options", "type": null}}, "command_line": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "selenium.webdriver.firefox.firefox_binary.FirefoxBinary.command_line", "name": "command_line", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "kill": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "selenium.webdriver.firefox.firefox_binary.FirefoxBinary.kill", "name": "kill", "type": null}}, "launch_browser": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "profile", "timeout"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "selenium.webdriver.firefox.firefox_binary.FirefoxBinary.launch_browser", "name": "launch_browser", "type": null}}, "platform": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "selenium.webdriver.firefox.firefox_binary.FirefoxBinary.platform", "name": "platform", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "process": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "selenium.webdriver.firefox.firefox_binary.FirefoxBinary.process", "name": "process", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "profile": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "selenium.webdriver.firefox.firefox_binary.FirefoxBinary.profile", "name": "profile", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "which": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "fname"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "selenium.webdriver.firefox.firefox_binary.FirefoxBinary.which", "name": "which", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "selenium.webdriver.firefox.firefox_binary.FirefoxBinary.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "selenium.webdriver.firefox.firefox_binary.FirefoxBinary", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Popen": {".class": "SymbolTableNode", "cross_ref": "subprocess.Popen", "kind": "Gdef"}, "STDOUT": {".class": "SymbolTableNode", "cross_ref": "subprocess.STDOUT", "kind": "Gdef"}, "WebDriverException": {".class": "SymbolTableNode", "cross_ref": "selenium.common.exceptions.WebDriverException", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "selenium.webdriver.firefox.firefox_binary.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "selenium.webdriver.firefox.firefox_binary.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "selenium.webdriver.firefox.firefox_binary.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "selenium.webdriver.firefox.firefox_binary.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "selenium.webdriver.firefox.firefox_binary.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "selenium.webdriver.firefox.firefox_binary.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "system": {".class": "SymbolTableNode", "cross_ref": "platform.system", "kind": "Gdef"}, "time": {".class": "SymbolTableNode", "cross_ref": "time", "kind": "Gdef"}, "utils": {".class": "SymbolTableNode", "cross_ref": "selenium.webdriver.common.utils", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\selenium\\webdriver\\firefox\\firefox_binary.py"}