{"data_mtime": 1757882372, "dep_lines": [18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30], "dependencies": ["selenium.webdriver.chrome.options", "selenium.webdriver.chrome.service", "selenium.webdriver.chrome.webdriver", "selenium.webdriver.common.action_chains", "selenium.webdriver.common.desired_capabilities", "selenium.webdriver.common.keys", "selenium.webdriver.common.proxy", "selenium.webdriver.edge.options", "selenium.webdriver.edge.service", "selenium.webdriver.edge.webdriver", "selenium.webdriver.firefox.firefox_profile", "selenium.webdriver.firefox.options", "selenium.webdriver.firefox.service", "selenium.webdriver.firefox.webdriver", "selenium.webdriver.ie.options", "selenium.webdriver.ie.service", "selenium.webdriver.ie.webdriver", "selenium.webdriver.remote.webdriver", "selenium.webdriver.safari.options", "selenium.webdriver.safari.service", "selenium.webdriver.safari.webdriver", "selenium.webdriver.webkitgtk.options", "selenium.webdriver.webkitgtk.service", "selenium.webdriver.webkitgtk.webdriver", "selenium.webdriver.wpewebkit.options", "selenium.webdriver.wpewebkit.service", "selenium.webdriver.wpewebkit.webdriver", "builtins", "_frozen_importlib", "abc", "typing"], "hash": "8f1b74b8ba4040a2e0d420e3fbe6055adcdb7112", "id": "selenium.webdriver", "ignore_all": true, "interface_hash": "6fdee5a1b2c09a7d0044e4fba90897a4f71fd390", "mtime": 1757882171, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\selenium\\webdriver\\__init__.py", "plugin_data": null, "size": 3067, "suppressed": [], "version_id": "1.15.0"}