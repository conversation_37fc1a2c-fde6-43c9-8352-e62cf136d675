{".class": "MypyFile", "_fullname": "selenium.common", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ElementClickInterceptedException": {".class": "SymbolTableNode", "cross_ref": "selenium.common.exceptions.ElementClickInterceptedException", "kind": "Gdef"}, "ElementNotInteractableException": {".class": "SymbolTableNode", "cross_ref": "selenium.common.exceptions.ElementNotInteractableException", "kind": "Gdef"}, "ElementNotSelectableException": {".class": "SymbolTableNode", "cross_ref": "selenium.common.exceptions.ElementNotSelectableException", "kind": "Gdef"}, "ElementNotVisibleException": {".class": "SymbolTableNode", "cross_ref": "selenium.common.exceptions.ElementNotVisibleException", "kind": "Gdef"}, "ImeActivationFailedException": {".class": "SymbolTableNode", "cross_ref": "selenium.common.exceptions.ImeActivationFailedException", "kind": "Gdef"}, "ImeNotAvailableException": {".class": "SymbolTableNode", "cross_ref": "selenium.common.exceptions.ImeNotAvailableException", "kind": "Gdef"}, "InsecureCertificateException": {".class": "SymbolTableNode", "cross_ref": "selenium.common.exceptions.InsecureCertificateException", "kind": "Gdef"}, "InvalidArgumentException": {".class": "SymbolTableNode", "cross_ref": "selenium.common.exceptions.InvalidArgumentException", "kind": "Gdef"}, "InvalidCookieDomainException": {".class": "SymbolTableNode", "cross_ref": "selenium.common.exceptions.InvalidCookieDomainException", "kind": "Gdef"}, "InvalidCoordinatesException": {".class": "SymbolTableNode", "cross_ref": "selenium.common.exceptions.InvalidCoordinatesException", "kind": "Gdef"}, "InvalidElementStateException": {".class": "SymbolTableNode", "cross_ref": "selenium.common.exceptions.InvalidElementStateException", "kind": "Gdef"}, "InvalidSelectorException": {".class": "SymbolTableNode", "cross_ref": "selenium.common.exceptions.InvalidSelectorException", "kind": "Gdef"}, "InvalidSessionIdException": {".class": "SymbolTableNode", "cross_ref": "selenium.common.exceptions.InvalidSessionIdException", "kind": "Gdef"}, "InvalidSwitchToTargetException": {".class": "SymbolTableNode", "cross_ref": "selenium.common.exceptions.InvalidSwitchToTargetException", "kind": "Gdef"}, "JavascriptException": {".class": "SymbolTableNode", "cross_ref": "selenium.common.exceptions.JavascriptException", "kind": "Gdef"}, "MoveTargetOutOfBoundsException": {".class": "SymbolTableNode", "cross_ref": "selenium.common.exceptions.MoveTargetOutOfBoundsException", "kind": "Gdef"}, "NoAlertPresentException": {".class": "SymbolTableNode", "cross_ref": "selenium.common.exceptions.NoAlertPresentException", "kind": "Gdef"}, "NoSuchAttributeException": {".class": "SymbolTableNode", "cross_ref": "selenium.common.exceptions.NoSuchAttributeException", "kind": "Gdef"}, "NoSuchCookieException": {".class": "SymbolTableNode", "cross_ref": "selenium.common.exceptions.NoSuchCookieException", "kind": "Gdef"}, "NoSuchDriverException": {".class": "SymbolTableNode", "cross_ref": "selenium.common.exceptions.NoSuchDriverException", "kind": "Gdef"}, "NoSuchElementException": {".class": "SymbolTableNode", "cross_ref": "selenium.common.exceptions.NoSuchElementException", "kind": "Gdef"}, "NoSuchFrameException": {".class": "SymbolTableNode", "cross_ref": "selenium.common.exceptions.NoSuchFrameException", "kind": "Gdef"}, "NoSuchShadowRootException": {".class": "SymbolTableNode", "cross_ref": "selenium.common.exceptions.NoSuchShadowRootException", "kind": "Gdef"}, "NoSuchWindowException": {".class": "SymbolTableNode", "cross_ref": "selenium.common.exceptions.NoSuchWindowException", "kind": "Gdef"}, "ScreenshotException": {".class": "SymbolTableNode", "cross_ref": "selenium.common.exceptions.ScreenshotException", "kind": "Gdef"}, "SessionNotCreatedException": {".class": "SymbolTableNode", "cross_ref": "selenium.common.exceptions.SessionNotCreatedException", "kind": "Gdef"}, "StaleElementReferenceException": {".class": "SymbolTableNode", "cross_ref": "selenium.common.exceptions.StaleElementReferenceException", "kind": "Gdef"}, "TimeoutException": {".class": "SymbolTableNode", "cross_ref": "selenium.common.exceptions.TimeoutException", "kind": "Gdef"}, "UnableToSetCookieException": {".class": "SymbolTableNode", "cross_ref": "selenium.common.exceptions.UnableToSetCookieException", "kind": "Gdef"}, "UnexpectedAlertPresentException": {".class": "SymbolTableNode", "cross_ref": "selenium.common.exceptions.UnexpectedAlertPresentException", "kind": "Gdef"}, "UnexpectedTagNameException": {".class": "SymbolTableNode", "cross_ref": "selenium.common.exceptions.UnexpectedTagNameException", "kind": "Gdef"}, "UnknownMethodException": {".class": "SymbolTableNode", "cross_ref": "selenium.common.exceptions.UnknownMethodException", "kind": "Gdef"}, "WebDriverException": {".class": "SymbolTableNode", "cross_ref": "selenium.common.exceptions.WebDriverException", "kind": "Gdef"}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "selenium.common.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "selenium.common.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "selenium.common.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "selenium.common.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "selenium.common.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "selenium.common.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "selenium.common.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "selenium.common.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\selenium\\common\\__init__.py"}