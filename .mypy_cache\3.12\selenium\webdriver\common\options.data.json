{".class": "MypyFile", "_fullname": "selenium.webdriver.common.options", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ABCMeta": {".class": "SymbolTableNode", "cross_ref": "abc.ABCMeta", "kind": "Gdef"}, "ArgOptions": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["selenium.webdriver.common.options.BaseOptions"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "selenium.webdriver.common.options.ArgOptions", "name": "ArgOptions", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "selenium.webdriver.common.options.ArgOptions", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "selenium.webdriver.common.options", "mro": ["selenium.webdriver.common.options.ArgOptions", "selenium.webdriver.common.options.BaseOptions", "builtins.object"], "names": {".class": "SymbolTable", "BINARY_LOCATION_ERROR": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "selenium.webdriver.common.options.ArgOptions.BINARY_LOCATION_ERROR", "name": "BINARY_LOCATION_ERROR", "type": "builtins.str"}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "selenium.webdriver.common.options.ArgOptions.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["selenium.webdriver.common.options.ArgOptions"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ArgOptions", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_arguments": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred", "invalid_partial_type"], "fullname": "selenium.webdriver.common.options.ArgOptions._arguments", "name": "_arguments", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "_ignore_local_proxy": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "selenium.webdriver.common.options.ArgOptions._ignore_local_proxy", "name": "_ignore_local_proxy", "type": "builtins.bool"}}, "add_argument": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "argument"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "selenium.webdriver.common.options.ArgOptions.add_argument", "name": "add_argument", "type": null}}, "arguments": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "selenium.webdriver.common.options.ArgOptions.arguments", "name": "arguments", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "selenium.webdriver.common.options.ArgOptions.arguments", "name": "arguments", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["selenium.webdriver.common.options.ArgOptions"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "arguments of ArgOptions", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "default_capabilities": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "selenium.webdriver.common.options.ArgOptions.default_capabilities", "name": "default_capabilities", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "selenium.webdriver.common.options.ArgOptions.default_capabilities", "name": "default_capabilities", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["selenium.webdriver.common.options.ArgOptions"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "default_capabilities of ArgOptions", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "ignore_local_proxy_environment_variables": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "selenium.webdriver.common.options.ArgOptions.ignore_local_proxy_environment_variables", "name": "ignore_local_proxy_environment_variables", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["selenium.webdriver.common.options.ArgOptions"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ignore_local_proxy_environment_variables of ArgOptions", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "to_capabilities": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "selenium.webdriver.common.options.ArgOptions.to_capabilities", "name": "to_capabilities", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "selenium.webdriver.common.options.ArgOptions.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "selenium.webdriver.common.options.ArgOptions", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BaseOptions": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["default_capabilities", 1], ["to_capabilities", 1]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": "abc.ABCMeta", "defn": {".class": "ClassDef", "fullname": "selenium.webdriver.common.options.BaseOptions", "name": "BaseOptions", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract"], "fullname": "selenium.webdriver.common.options.BaseOptions", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "selenium.webdriver.common.options", "mro": ["selenium.webdriver.common.options.BaseOptions", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "selenium.webdriver.common.options.BaseOptions.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["selenium.webdriver.common.options.BaseOptions"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of BaseOptions", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_caps": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "selenium.webdriver.common.options.BaseOptions._caps", "name": "_caps", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}}}, "_proxy": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "selenium.webdriver.common.options.BaseOptions._proxy", "name": "_proxy", "type": {".class": "NoneType"}}}, "accept_insecure_certs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "selenium.webdriver.common.options.BaseOptions.accept_insecure_certs", "name": "accept_insecure_certs", "type": "selenium.webdriver.common.options._BaseOptionsDescriptor"}}, "browser_version": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "selenium.webdriver.common.options.BaseOptions.browser_version", "name": "browser_version", "type": "selenium.webdriver.common.options._BaseOptionsDescriptor"}}, "capabilities": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "selenium.webdriver.common.options.BaseOptions.capabilities", "name": "capabilities", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "selenium.webdriver.common.options.BaseOptions.capabilities", "name": "capabilities", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["selenium.webdriver.common.options.BaseOptions"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "capabilities of BaseOptions", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "default_capabilities": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_body"], "fullname": "selenium.webdriver.common.options.BaseOptions.default_capabilities", "name": "default_capabilities", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "selenium.webdriver.common.options.BaseOptions.default_capabilities", "name": "default_capabilities", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["selenium.webdriver.common.options.BaseOptions"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "default_capabilities of BaseOptions", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "enable_downloads": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "selenium.webdriver.common.options.BaseOptions.enable_downloads", "name": "enable_downloads", "type": "selenium.webdriver.common.options._BaseOptionsDescriptor"}}, "enable_mobile": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "android_package", "android_activity", "device_serial"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "selenium.webdriver.common.options.BaseOptions.enable_mobile", "name": "enable_mobile", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "android_package", "android_activity", "device_serial"], "arg_types": ["selenium.webdriver.common.options.BaseOptions", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "enable_mobile of BaseOptions", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "mobile_options": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "selenium.webdriver.common.options.BaseOptions.mobile_options", "name": "mobile_options", "type": {".class": "NoneType"}}}, "page_load_strategy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "selenium.webdriver.common.options.BaseOptions.page_load_strategy", "name": "page_load_strategy", "type": "selenium.webdriver.common.options._PageLoadStrategyDescriptor"}}, "platform_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "selenium.webdriver.common.options.BaseOptions.platform_name", "name": "platform_name", "type": "selenium.webdriver.common.options._BaseOptionsDescriptor"}}, "proxy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "selenium.webdriver.common.options.BaseOptions.proxy", "name": "proxy", "type": "selenium.webdriver.common.options._ProxyDescriptor"}}, "set_capability": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "selenium.webdriver.common.options.BaseOptions.set_capability", "name": "set_capability", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "value"], "arg_types": ["selenium.webdriver.common.options.BaseOptions", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_capability of BaseOptions", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_window_rect": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "selenium.webdriver.common.options.BaseOptions.set_window_rect", "name": "set_window_rect", "type": "selenium.webdriver.common.options._BaseOptionsDescriptor"}}, "strict_file_interactability": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "selenium.webdriver.common.options.BaseOptions.strict_file_interactability", "name": "strict_file_interactability", "type": "selenium.webdriver.common.options._BaseOptionsDescriptor"}}, "timeouts": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "selenium.webdriver.common.options.BaseOptions.timeouts", "name": "timeouts", "type": "selenium.webdriver.common.options._TimeoutsDescriptor"}}, "to_capabilities": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "selenium.webdriver.common.options.BaseOptions.to_capabilities", "name": "to_capabilities", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "selenium.webdriver.common.options.BaseOptions.to_capabilities", "name": "to_capabilities", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["selenium.webdriver.common.options.BaseOptions"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_capabilities of BaseOptions", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "unhandled_prompt_behavior": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "selenium.webdriver.common.options.BaseOptions.unhandled_prompt_behavior", "name": "unhandled_prompt_behavior", "type": "selenium.webdriver.common.options._UnHandledPromptBehaviorDescriptor"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "selenium.webdriver.common.options.BaseOptions.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "selenium.webdriver.common.options.BaseOptions", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InvalidArgumentException": {".class": "SymbolTableNode", "cross_ref": "selenium.common.exceptions.InvalidArgumentException", "kind": "Gdef"}, "Proxy": {".class": "SymbolTableNode", "cross_ref": "selenium.webdriver.common.proxy.Proxy", "kind": "Gdef"}, "_BaseOptionsDescriptor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "selenium.webdriver.common.options._BaseOptionsDescriptor", "name": "_BaseOptionsDescriptor", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "selenium.webdriver.common.options._BaseOptionsDescriptor", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "selenium.webdriver.common.options", "mro": ["selenium.webdriver.common.options._BaseOptionsDescriptor", "builtins.object"], "names": {".class": "SymbolTable", "__get__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "obj", "cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "selenium.webdriver.common.options._BaseOptionsDescriptor.__get__", "name": "__get__", "type": null}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "selenium.webdriver.common.options._BaseOptionsDescriptor.__init__", "name": "__init__", "type": null}}, "__set__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "obj", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "selenium.webdriver.common.options._BaseOptionsDescriptor.__set__", "name": "__set__", "type": null}}, "name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "selenium.webdriver.common.options._BaseOptionsDescriptor.name", "name": "name", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "selenium.webdriver.common.options._BaseOptionsDescriptor.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "selenium.webdriver.common.options._BaseOptionsDescriptor", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_PageLoadStrategyDescriptor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "selenium.webdriver.common.options._PageLoadStrategyDescriptor", "name": "_PageLoadStrategyDescriptor", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "selenium.webdriver.common.options._PageLoadStrategyDescriptor", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "selenium.webdriver.common.options", "mro": ["selenium.webdriver.common.options._PageLoadStrategyDescriptor", "builtins.object"], "names": {".class": "SymbolTable", "__get__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "obj", "cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "selenium.webdriver.common.options._PageLoadStrategyDescriptor.__get__", "name": "__get__", "type": null}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "selenium.webdriver.common.options._PageLoadStrategyDescriptor.__init__", "name": "__init__", "type": null}}, "__set__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "obj", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "selenium.webdriver.common.options._PageLoadStrategyDescriptor.__set__", "name": "__set__", "type": null}}, "name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "selenium.webdriver.common.options._PageLoadStrategyDescriptor.name", "name": "name", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "selenium.webdriver.common.options._PageLoadStrategyDescriptor.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "selenium.webdriver.common.options._PageLoadStrategyDescriptor", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_ProxyDescriptor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "selenium.webdriver.common.options._ProxyDescriptor", "name": "_ProxyDescriptor", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "selenium.webdriver.common.options._ProxyDescriptor", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "selenium.webdriver.common.options", "mro": ["selenium.webdriver.common.options._ProxyDescriptor", "builtins.object"], "names": {".class": "SymbolTable", "__get__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "obj", "cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "selenium.webdriver.common.options._ProxyDescriptor.__get__", "name": "__get__", "type": null}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "selenium.webdriver.common.options._ProxyDescriptor.__init__", "name": "__init__", "type": null}}, "__set__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "obj", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "selenium.webdriver.common.options._ProxyDescriptor.__set__", "name": "__set__", "type": null}}, "name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "selenium.webdriver.common.options._ProxyDescriptor.name", "name": "name", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "selenium.webdriver.common.options._ProxyDescriptor.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "selenium.webdriver.common.options._ProxyDescriptor", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_TimeoutsDescriptor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "selenium.webdriver.common.options._TimeoutsDescriptor", "name": "_TimeoutsDescriptor", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "selenium.webdriver.common.options._TimeoutsDescriptor", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "selenium.webdriver.common.options", "mro": ["selenium.webdriver.common.options._TimeoutsDescriptor", "builtins.object"], "names": {".class": "SymbolTable", "__get__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "obj", "cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "selenium.webdriver.common.options._TimeoutsDescriptor.__get__", "name": "__get__", "type": null}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "selenium.webdriver.common.options._TimeoutsDescriptor.__init__", "name": "__init__", "type": null}}, "__set__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "obj", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "selenium.webdriver.common.options._TimeoutsDescriptor.__set__", "name": "__set__", "type": null}}, "name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "selenium.webdriver.common.options._TimeoutsDescriptor.name", "name": "name", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "selenium.webdriver.common.options._TimeoutsDescriptor.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "selenium.webdriver.common.options._TimeoutsDescriptor", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_UnHandledPromptBehaviorDescriptor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "selenium.webdriver.common.options._UnHandledPromptBehaviorDescriptor", "name": "_UnHandledPromptBehaviorDescriptor", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "selenium.webdriver.common.options._UnHandledPromptBehaviorDescriptor", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "selenium.webdriver.common.options", "mro": ["selenium.webdriver.common.options._UnHandledPromptBehaviorDescriptor", "builtins.object"], "names": {".class": "SymbolTable", "__get__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "obj", "cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "selenium.webdriver.common.options._UnHandledPromptBehaviorDescriptor.__get__", "name": "__get__", "type": null}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "selenium.webdriver.common.options._UnHandledPromptBehaviorDescriptor.__init__", "name": "__init__", "type": null}}, "__set__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "obj", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "selenium.webdriver.common.options._UnHandledPromptBehaviorDescriptor.__set__", "name": "__set__", "type": null}}, "name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "selenium.webdriver.common.options._UnHandledPromptBehaviorDescriptor.name", "name": "name", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "selenium.webdriver.common.options._UnHandledPromptBehaviorDescriptor.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "selenium.webdriver.common.options._UnHandledPromptBehaviorDescriptor", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "selenium.webdriver.common.options.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "selenium.webdriver.common.options.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "selenium.webdriver.common.options.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "selenium.webdriver.common.options.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "selenium.webdriver.common.options.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "selenium.webdriver.common.options.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "abstractmethod": {".class": "SymbolTableNode", "cross_ref": "abc.abstractmethod", "kind": "Gdef"}, "typing": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\selenium\\webdriver\\common\\options.py"}