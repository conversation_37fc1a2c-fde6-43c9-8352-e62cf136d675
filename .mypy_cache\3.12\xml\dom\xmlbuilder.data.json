{".class": "MypyFile", "_fullname": "xml.dom.xmlbuilder", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DOMBuilder": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "xml.dom.xmlbuilder.DOMBuilder", "name": "DOMBuilder", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "xml.dom.xmlbuilder.DOMBuilder", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "xml.dom.xmlbuilder", "mro": ["xml.dom.xmlbuilder.DOMBuilder", "builtins.object"], "names": {".class": "SymbolTable", "ACTION_APPEND_AS_CHILDREN": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "xml.dom.xmlbuilder.DOMBuilder.ACTION_APPEND_AS_CHILDREN", "name": "ACTION_APPEND_AS_CHILDREN", "type": {".class": "LiteralType", "fallback": "builtins.int", "value": 2}}}, "ACTION_INSERT_AFTER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "xml.dom.xmlbuilder.DOMBuilder.ACTION_INSERT_AFTER", "name": "ACTION_INSERT_AFTER", "type": {".class": "LiteralType", "fallback": "builtins.int", "value": 3}}}, "ACTION_INSERT_BEFORE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "xml.dom.xmlbuilder.DOMBuilder.ACTION_INSERT_BEFORE", "name": "ACTION_INSERT_BEFORE", "type": {".class": "LiteralType", "fallback": "builtins.int", "value": 4}}}, "ACTION_REPLACE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "xml.dom.xmlbuilder.DOMBuilder.ACTION_REPLACE", "name": "ACTION_REPLACE", "type": {".class": "LiteralType", "fallback": "builtins.int", "value": 1}}}, "canSetFeature": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "state"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "xml.dom.xmlbuilder.DOMBuilder.canSetFeature", "name": "canSetFeature", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "state"], "arg_types": ["xml.dom.xmlbuilder.DOMBuilder", "builtins.str", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "canSetFeature of DOMBuilder", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "entityResolver": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "xml.dom.xmlbuilder.DOMBuilder.entityResolver", "name": "entityResolver", "type": {".class": "UnionType", "items": ["xml.dom.xmlbuilder.DOMEntityResolver", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "errorHandler": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "xml.dom.xmlbuilder.DOMBuilder.errorHandler", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": {".class": "TypeAliasType", "args": [], "type_ref": "xml.dom.xmlbuilder._DOMBuilderErrorHandlerType"}}}, "filter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "xml.dom.xmlbuilder.DOMBuilder.filter", "name": "filter", "type": {".class": "UnionType", "items": ["xml.dom.xmlbuilder.DOMBuilderFilter", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "getFeature": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "xml.dom.xmlbuilder.DOMBuilder.getFeature", "name": "getFeature", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "name"], "arg_types": ["xml.dom.xmlbuilder.DOMBuilder", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getFeature of DOMBuilder", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parse": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "input"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "xml.dom.xmlbuilder.DOMBuilder.parse", "name": "parse", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "input"], "arg_types": ["xml.dom.xmlbuilder.DOMBuilder", "xml.dom.xmlbuilder.DOMInputSource"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse of DOMBuilder", "ret_type": {".class": "UnionType", "items": ["xml.dom.expatbuilder.ExpatBuilder", "xml.dom.expatbuilder.ExpatBuilderNS"], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parseURI": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "uri"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "xml.dom.xmlbuilder.DOMBuilder.parseURI", "name": "parseURI", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "uri"], "arg_types": ["xml.dom.xmlbuilder.DOMBuilder", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parseURI of DOMBuilder", "ret_type": {".class": "UnionType", "items": ["xml.dom.expatbuilder.ExpatBuilder", "xml.dom.expatbuilder.ExpatBuilderNS"], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parseWithContext": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "input", "cnode", "action"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "xml.dom.xmlbuilder.DOMBuilder.parseWithContext", "name": "parseWithContext", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "input", "cnode", "action"], "arg_types": ["xml.dom.xmlbuilder.DOMBuilder", "builtins.object", "builtins.object", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.int", "value": 1}, {".class": "LiteralType", "fallback": "builtins.int", "value": 2}, {".class": "LiteralType", "fallback": "builtins.int", "value": 3}, {".class": "LiteralType", "fallback": "builtins.int", "value": 4}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parseWithContext of DOMBuilder", "ret_type": {".class": "UninhabitedType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "setFeature": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "state"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "xml.dom.xmlbuilder.DOMBuilder.setFeature", "name": "setFeature", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "state"], "arg_types": ["xml.dom.xmlbuilder.DOMBuilder", "builtins.str", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setFeature of DOMBuilder", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "supportsFeature": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "xml.dom.xmlbuilder.DOMBuilder.supportsFeature", "name": "supportsFeature", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "name"], "arg_types": ["xml.dom.xmlbuilder.DOMBuilder", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "supportsFeature of DOMBuilder", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "xml.dom.xmlbuilder.DOMBuilder.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "xml.dom.xmlbuilder.DOMBuilder", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DOMBuilderFilter": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "xml.dom.xmlbuilder.DOMBuilderFilter", "name": "DOMBuilderFilter", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "xml.dom.xmlbuilder.DOMBuilderFilter", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "xml.dom.xmlbuilder", "mro": ["xml.dom.xmlbuilder.DOMBuilderFilter", "builtins.object"], "names": {".class": "SymbolTable", "FILTER_ACCEPT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "xml.dom.xmlbuilder.DOMBuilderFilter.FILTER_ACCEPT", "name": "FILTER_ACCEPT", "type": {".class": "LiteralType", "fallback": "builtins.int", "value": 1}}}, "FILTER_INTERRUPT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "xml.dom.xmlbuilder.DOMBuilderFilter.FILTER_INTERRUPT", "name": "FILTER_INTERRUPT", "type": {".class": "LiteralType", "fallback": "builtins.int", "value": 4}}}, "FILTER_REJECT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "xml.dom.xmlbuilder.DOMBuilderFilter.FILTER_REJECT", "name": "FILTER_REJECT", "type": {".class": "LiteralType", "fallback": "builtins.int", "value": 2}}}, "FILTER_SKIP": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "xml.dom.xmlbuilder.DOMBuilderFilter.FILTER_SKIP", "name": "FILTER_SKIP", "type": {".class": "LiteralType", "fallback": "builtins.int", "value": 3}}}, "acceptNode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "element"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "xml.dom.xmlbuilder.DOMBuilderFilter.acceptNode", "name": "acceptNode", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "element"], "arg_types": ["xml.dom.xmlbuilder.DOMBuilderFilter", "builtins.object"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "acceptNode of DOMBuilderFilter", "ret_type": {".class": "LiteralType", "fallback": "builtins.int", "value": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "startContainer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "element"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "xml.dom.xmlbuilder.DOMBuilderFilter.startContainer", "name": "startContainer", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "element"], "arg_types": ["xml.dom.xmlbuilder.DOMBuilderFilter", "builtins.object"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "startContainer of DOMBuilderFilter", "ret_type": {".class": "LiteralType", "fallback": "builtins.int", "value": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "whatToShow": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "xml.dom.xmlbuilder.DOMBuilderFilter.whatToShow", "name": "whatToShow", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "xml.dom.xmlbuilder.DOMBuilderFilter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "xml.dom.xmlbuilder.DOMBuilderFilter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DOMEntityResolver": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "xml.dom.xmlbuilder.DOMEntityResolver", "name": "DOMEntityResolver", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "xml.dom.xmlbuilder.DOMEntityResolver", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "xml.dom.xmlbuilder", "mro": ["xml.dom.xmlbuilder.DOMEntityResolver", "builtins.object"], "names": {".class": "SymbolTable", "resolveEntity": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "publicId", "systemId"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "xml.dom.xmlbuilder.DOMEntityResolver.resolveEntity", "name": "resolveEntity", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "publicId", "systemId"], "arg_types": ["xml.dom.xmlbuilder.DOMEntityResolver", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "resolveEntity of DOMEntityResolver", "ret_type": "xml.dom.xmlbuilder.DOMInputSource", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "xml.dom.xmlbuilder.DOMEntityResolver.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "xml.dom.xmlbuilder.DOMEntityResolver", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DOMImplementationLS": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "xml.dom.xmlbuilder.DOMImplementationLS", "name": "DOMImplementationLS", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "xml.dom.xmlbuilder.DOMImplementationLS", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "xml.dom.xmlbuilder", "mro": ["xml.dom.xmlbuilder.DOMImplementationLS", "builtins.object"], "names": {".class": "SymbolTable", "MODE_ASYNCHRONOUS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "xml.dom.xmlbuilder.DOMImplementationLS.MODE_ASYNCHRONOUS", "name": "MODE_ASYNCHRONOUS", "type": {".class": "LiteralType", "fallback": "builtins.int", "value": 2}}}, "MODE_SYNCHRONOUS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "xml.dom.xmlbuilder.DOMImplementationLS.MODE_SYNCHRONOUS", "name": "MODE_SYNCHRONOUS", "type": {".class": "LiteralType", "fallback": "builtins.int", "value": 1}}}, "createDOMBuilder": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "mode", "schemaType"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "xml.dom.xmlbuilder.DOMImplementationLS.createDOMBuilder", "name": "createDOMBuilder", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "mode", "schemaType"], "arg_types": ["xml.dom.xmlbuilder.DOMImplementationLS", {".class": "LiteralType", "fallback": "builtins.int", "value": 1}, {".class": "NoneType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "createDOMBuilder of DOMImplementationLS", "ret_type": "xml.dom.xmlbuilder.DOMBuilder", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "createDOMInputSource": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "xml.dom.xmlbuilder.DOMImplementationLS.createDOMInputSource", "name": "createDOMInputSource", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["xml.dom.xmlbuilder.DOMImplementationLS"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "createDOMInputSource of DOMImplementationLS", "ret_type": "xml.dom.xmlbuilder.DOMInputSource", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "createDOMWriter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "xml.dom.xmlbuilder.DOMImplementationLS.createDOMWriter", "name": "createDOMWriter", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["xml.dom.xmlbuilder.DOMImplementationLS"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "createDOMWriter of DOMImplementationLS", "ret_type": {".class": "UninhabitedType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "xml.dom.xmlbuilder.DOMImplementationLS.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "xml.dom.xmlbuilder.DOMImplementationLS", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DOMInputSource": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "xml.dom.xmlbuilder.DOMInputSource", "name": "DOMInputSource", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "xml.dom.xmlbuilder.DOMInputSource", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "xml.dom.xmlbuilder", "mro": ["xml.dom.xmlbuilder.DOMInputSource", "builtins.object"], "names": {".class": "SymbolTable", "baseURI": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "xml.dom.xmlbuilder.DOMInputSource.baseURI", "name": "baseURI", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "byteStream": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "xml.dom.xmlbuilder.DOMInputSource.byteStream", "name": "byteStream", "type": {".class": "UnionType", "items": ["urllib.request.OpenerDirector", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "characterStream": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "xml.dom.xmlbuilder.DOMInputSource.characterStream", "name": "characterStream", "type": {".class": "TypeAliasType", "args": [], "type_ref": "xml.dom.xmlbuilder._DOMInputSourceCharacterStreamType"}}}, "encoding": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "xml.dom.xmlbuilder.DOMInputSource.encoding", "name": "encoding", "type": {".class": "TypeAliasType", "args": [], "type_ref": "xml.dom.xmlbuilder._DOMInputSourceEncodingType"}}}, "publicId": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "xml.dom.xmlbuilder.DOMInputSource.publicId", "name": "publicId", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "stringData": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "xml.dom.xmlbuilder.DOMInputSource.stringData", "name": "stringData", "type": {".class": "TypeAliasType", "args": [], "type_ref": "xml.dom.xmlbuilder._DOMInputSourceStringDataType"}}}, "systemId": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "xml.dom.xmlbuilder.DOMInputSource.systemId", "name": "systemId", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "xml.dom.xmlbuilder.DOMInputSource.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "xml.dom.xmlbuilder.DOMInputSource", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DocumentLS": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "xml.dom.xmlbuilder.DocumentLS", "name": "DocumentLS", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "xml.dom.xmlbuilder.DocumentLS", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "xml.dom.xmlbuilder", "mro": ["xml.dom.xmlbuilder.DocumentLS", "builtins.object"], "names": {".class": "SymbolTable", "abort": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "xml.dom.xmlbuilder.DocumentLS.abort", "name": "abort", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["xml.dom.xmlbuilder.DocumentLS"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "abort of DocumentLS", "ret_type": {".class": "UninhabitedType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "async_": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "xml.dom.xmlbuilder.DocumentLS.async_", "name": "async_", "type": "builtins.bool"}}, "load": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "uri"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "xml.dom.xmlbuilder.DocumentLS.load", "name": "load", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "uri"], "arg_types": ["xml.dom.xmlbuilder.DocumentLS", "builtins.object"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "load of DocumentLS", "ret_type": {".class": "UninhabitedType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "loadXML": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "source"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "xml.dom.xmlbuilder.DocumentLS.loadXML", "name": "loadXML", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "source"], "arg_types": ["xml.dom.xmlbuilder.DocumentLS", "builtins.object"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "loadXML of DocumentLS", "ret_type": {".class": "UninhabitedType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "saveXML": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "snode"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "xml.dom.xmlbuilder.DocumentLS.saveXML", "name": "saveXML", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "snode"], "arg_types": ["xml.dom.xmlbuilder.DocumentLS", {".class": "UnionType", "items": ["xml.dom.minidom.Node", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "saveXML of DocumentLS", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "xml.dom.xmlbuilder.DocumentLS.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "xml.dom.xmlbuilder.DocumentLS", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ExpatBuilder": {".class": "SymbolTableNode", "cross_ref": "xml.dom.expatbuilder.ExpatBuilder", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ExpatBuilderNS": {".class": "SymbolTableNode", "cross_ref": "xml.dom.expatbuilder.ExpatBuilderNS", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Incomplete": {".class": "SymbolTableNode", "cross_ref": "_typeshed.Incomplete", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef", "module_hidden": true, "module_public": false}, "NoReturn": {".class": "SymbolTableNode", "cross_ref": "typing.NoReturn", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Node": {".class": "SymbolTableNode", "cross_ref": "xml.dom.minidom.Node", "kind": "Gdef", "module_hidden": true, "module_public": false}, "OpenerDirector": {".class": "SymbolTableNode", "cross_ref": "urllib.request.OpenerDirector", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Options": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "xml.dom.xmlbuilder.Options", "name": "Options", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "xml.dom.xmlbuilder.Options", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "xml.dom.xmlbuilder", "mro": ["xml.dom.xmlbuilder.Options", "builtins.object"], "names": {".class": "SymbolTable", "cdata_sections": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "xml.dom.xmlbuilder.Options.cdata_sections", "name": "cdata_sections", "type": "builtins.bool"}}, "charset_overrides_xml_encoding": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "xml.dom.xmlbuilder.Options.charset_overrides_xml_encoding", "name": "charset_overrides_xml_encoding", "type": "builtins.bool"}}, "comments": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "xml.dom.xmlbuilder.Options.comments", "name": "comments", "type": "builtins.bool"}}, "create_entity_ref_nodes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "xml.dom.xmlbuilder.Options.create_entity_ref_nodes", "name": "create_entity_ref_nodes", "type": "builtins.bool"}}, "datatype_normalization": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "xml.dom.xmlbuilder.Options.datatype_normalization", "name": "datatype_normalization", "type": "builtins.bool"}}, "entities": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "xml.dom.xmlbuilder.Options.entities", "name": "entities", "type": "builtins.bool"}}, "errorHandler": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "xml.dom.xmlbuilder.Options.errorHandler", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "external_dtd_subset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "xml.dom.xmlbuilder.Options.external_dtd_subset", "name": "external_dtd_subset", "type": "builtins.bool"}}, "external_general_entities": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "xml.dom.xmlbuilder.Options.external_general_entities", "name": "external_general_entities", "type": "builtins.bool"}}, "external_parameter_entities": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "xml.dom.xmlbuilder.Options.external_parameter_entities", "name": "external_parameter_entities", "type": "builtins.bool"}}, "filter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "xml.dom.xmlbuilder.Options.filter", "name": "filter", "type": {".class": "UnionType", "items": ["xml.dom.xmlbuilder.DOMBuilderFilter", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "infoset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "xml.dom.xmlbuilder.Options.infoset", "name": "infoset", "type": "builtins.bool"}}, "namespace_declarations": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "xml.dom.xmlbuilder.Options.namespace_declarations", "name": "namespace_declarations", "type": "builtins.bool"}}, "namespaces": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "xml.dom.xmlbuilder.Options.namespaces", "name": "namespaces", "type": "builtins.int"}}, "supported_mediatypes_only": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "xml.dom.xmlbuilder.Options.supported_mediatypes_only", "name": "supported_mediatypes_only", "type": "builtins.bool"}}, "validate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "xml.dom.xmlbuilder.Options.validate", "name": "validate", "type": "builtins.bool"}}, "validate_if_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "xml.dom.xmlbuilder.Options.validate_if_schema", "name": "validate_if_schema", "type": "builtins.bool"}}, "validation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "xml.dom.xmlbuilder.Options.validation", "name": "validation", "type": "builtins.bool"}}, "whitespace_in_element_content": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "xml.dom.xmlbuilder.Options.whitespace_in_element_content", "name": "whitespace_in_element_content", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "xml.dom.xmlbuilder.Options.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "xml.dom.xmlbuilder.Options", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TypeAlias": {".class": "SymbolTableNode", "cross_ref": "typing.TypeAlias", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Unused": {".class": "SymbolTableNode", "cross_ref": "_typeshed.Unused", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_DOMBuilderErrorHandlerType": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "xml.dom.xmlbuilder._DOMBuilderErrorHandlerType", "line": 22, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.Incomplete"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "_DOMInputSourceCharacterStreamType": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "xml.dom.xmlbuilder._DOMInputSourceCharacterStreamType", "line": 24, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.Incomplete"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "_DOMInputSourceEncodingType": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "xml.dom.xmlbuilder._DOMInputSourceEncodingType", "line": 28, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.Incomplete"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "_DOMInputSourceStringDataType": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "xml.dom.xmlbuilder._DOMInputSourceStringDataType", "line": 26, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.Incomplete"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "xml.dom.xmlbuilder.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "xml.dom.xmlbuilder.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "xml.dom.xmlbuilder.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "xml.dom.xmlbuilder.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "xml.dom.xmlbuilder.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "xml.dom.xmlbuilder.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "xml.dom.xmlbuilder.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}}, "path": "c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.mypy-type-checker-2025.2.0\\bundled\\libs\\mypy\\typeshed\\stdlib\\xml\\dom\\xmlbuilder.pyi"}