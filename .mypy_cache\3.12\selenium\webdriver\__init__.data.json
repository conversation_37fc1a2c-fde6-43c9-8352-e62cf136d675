{".class": "MypyFile", "_fullname": "selenium.webdriver", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ActionChains": {".class": "SymbolTableNode", "cross_ref": "selenium.webdriver.common.action_chains.ActionChains", "kind": "Gdef"}, "Chrome": {".class": "SymbolTableNode", "cross_ref": "selenium.webdriver.chrome.webdriver.WebDriver", "kind": "Gdef"}, "ChromeOptions": {".class": "SymbolTableNode", "cross_ref": "selenium.webdriver.chrome.options.Options", "kind": "Gdef"}, "ChromeService": {".class": "SymbolTableNode", "cross_ref": "selenium.webdriver.chrome.service.Service", "kind": "Gdef"}, "ChromiumEdge": {".class": "SymbolTableNode", "cross_ref": "selenium.webdriver.edge.webdriver.WebDriver", "kind": "Gdef"}, "DesiredCapabilities": {".class": "SymbolTableNode", "cross_ref": "selenium.webdriver.common.desired_capabilities.DesiredCapabilities", "kind": "Gdef"}, "Edge": {".class": "SymbolTableNode", "cross_ref": "selenium.webdriver.edge.webdriver.WebDriver", "kind": "Gdef"}, "EdgeOptions": {".class": "SymbolTableNode", "cross_ref": "selenium.webdriver.edge.options.Options", "kind": "Gdef"}, "EdgeService": {".class": "SymbolTableNode", "cross_ref": "selenium.webdriver.edge.service.Service", "kind": "Gdef"}, "Firefox": {".class": "SymbolTableNode", "cross_ref": "selenium.webdriver.firefox.webdriver.WebDriver", "kind": "Gdef"}, "FirefoxOptions": {".class": "SymbolTableNode", "cross_ref": "selenium.webdriver.firefox.options.Options", "kind": "Gdef"}, "FirefoxProfile": {".class": "SymbolTableNode", "cross_ref": "selenium.webdriver.firefox.firefox_profile.FirefoxProfile", "kind": "Gdef"}, "FirefoxService": {".class": "SymbolTableNode", "cross_ref": "selenium.webdriver.firefox.service.Service", "kind": "Gdef"}, "Ie": {".class": "SymbolTableNode", "cross_ref": "selenium.webdriver.ie.webdriver.WebDriver", "kind": "Gdef"}, "IeOptions": {".class": "SymbolTableNode", "cross_ref": "selenium.webdriver.ie.options.Options", "kind": "Gdef"}, "IeService": {".class": "SymbolTableNode", "cross_ref": "selenium.webdriver.ie.service.Service", "kind": "Gdef"}, "Keys": {".class": "SymbolTableNode", "cross_ref": "selenium.webdriver.common.keys.Keys", "kind": "Gdef"}, "Proxy": {".class": "SymbolTableNode", "cross_ref": "selenium.webdriver.common.proxy.Proxy", "kind": "Gdef"}, "Remote": {".class": "SymbolTableNode", "cross_ref": "selenium.webdriver.remote.webdriver.WebDriver", "kind": "Gdef"}, "Safari": {".class": "SymbolTableNode", "cross_ref": "selenium.webdriver.safari.webdriver.WebDriver", "kind": "Gdef"}, "SafariOptions": {".class": "SymbolTableNode", "cross_ref": "selenium.webdriver.safari.options.Options", "kind": "Gdef"}, "SafariService": {".class": "SymbolTableNode", "cross_ref": "selenium.webdriver.safari.service.Service", "kind": "Gdef"}, "WPEWebKit": {".class": "SymbolTableNode", "cross_ref": "selenium.webdriver.wpewebkit.webdriver.WebDriver", "kind": "Gdef"}, "WPEWebKitOptions": {".class": "SymbolTableNode", "cross_ref": "selenium.webdriver.wpewebkit.options.Options", "kind": "Gdef"}, "WPEWebKitService": {".class": "SymbolTableNode", "cross_ref": "selenium.webdriver.wpewebkit.service.Service", "kind": "Gdef"}, "WebKitGTK": {".class": "SymbolTableNode", "cross_ref": "selenium.webdriver.webkitgtk.webdriver.WebDriver", "kind": "Gdef"}, "WebKitGTKOptions": {".class": "SymbolTableNode", "cross_ref": "selenium.webdriver.webkitgtk.options.Options", "kind": "Gdef"}, "WebKitGTKService": {".class": "SymbolTableNode", "cross_ref": "selenium.webdriver.webkitgtk.service.Service", "kind": "Gdef"}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "selenium.webdriver.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "selenium.webdriver.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "selenium.webdriver.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "selenium.webdriver.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "selenium.webdriver.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "selenium.webdriver.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "selenium.webdriver.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "selenium.webdriver.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "__version__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "selenium.webdriver.__version__", "name": "__version__", "type": "builtins.str"}}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\selenium\\webdriver\\__init__.py"}