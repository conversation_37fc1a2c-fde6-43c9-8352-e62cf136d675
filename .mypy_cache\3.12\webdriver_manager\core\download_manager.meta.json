{"data_mtime": 1757882372, "dep_lines": [4, 5, 6, 1, 2, 1, 1, 1], "dep_prios": [5, 5, 5, 10, 5, 5, 30, 30], "dependencies": ["webdriver_manager.core.file_manager", "webdriver_manager.core.http", "webdriver_manager.core.logger", "os", "abc", "builtins", "_frozen_importlib", "typing"], "hash": "e90892ba8a4b44f94ef12121d439533008044602", "id": "webdriver_manager.core.download_manager", "ignore_all": true, "interface_hash": "b82b1e2343b5051ccc7296fb5d9de8ce6d75bbdd", "mtime": 1757882167, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\webdriver_manager\\core\\download_manager.py", "plugin_data": null, "size": 1303, "suppressed": [], "version_id": "1.15.0"}