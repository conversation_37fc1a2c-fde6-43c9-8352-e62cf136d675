{"data_mtime": 1757882372, "dep_lines": [20, 20, 19, 17, 1, 1, 1, 1, 1], "dep_prios": [10, 20, 5, 10, 5, 30, 30, 30, 30], "dependencies": ["selenium.webdriver.chromium.service", "selenium.webdriver.chromium", "selenium.types", "typing", "builtins", "_frozen_importlib", "abc", "selenium.webdriver.common", "selenium.webdriver.common.service"], "hash": "f50466c01771e21a873c776f0b09deda6d623ba6", "id": "selenium.webdriver.chrome.service", "ignore_all": true, "interface_hash": "ec2b5493198baa10631ab206eb3b1279155ee625", "mtime": 1757882171, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\selenium\\webdriver\\chrome\\service.py", "plugin_data": null, "size": 2091, "suppressed": [], "version_id": "1.15.0"}