{"data_mtime": 1757882368, "dep_lines": [3, 1, 10, 1, 1, 1], "dep_prios": [5, 5, 10, 5, 30, 30], "dependencies": ["importlib.metadata", "__future__", "typing", "builtins", "_frozen_importlib", "abc"], "hash": "3e0de2068e4b0cdff235a49d6acda3db24d4aec2", "id": "urllib3.http2", "ignore_all": true, "interface_hash": "b7386cb5d05879b2e0d650dd11e827fead333b6a", "mtime": 1757882164, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\urllib3\\http2\\__init__.py", "plugin_data": null, "size": 1741, "suppressed": [], "version_id": "1.15.0"}