{".class": "MypyFile", "_fullname": "selenium.webdriver.remote.command", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Command": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "selenium.webdriver.remote.command.Command", "name": "Command", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "selenium.webdriver.remote.command.Command", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "selenium.webdriver.remote.command", "mro": ["selenium.webdriver.remote.command.Command", "builtins.object"], "names": {".class": "SymbolTable", "ADD_COOKIE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "selenium.webdriver.remote.command.Command.ADD_COOKIE", "name": "ADD_COOKIE", "type": "builtins.str"}}, "ADD_CREDENTIAL": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "selenium.webdriver.remote.command.Command.ADD_CREDENTIAL", "name": "ADD_CREDENTIAL", "type": "builtins.str"}}, "ADD_VIRTUAL_AUTHENTICATOR": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "selenium.webdriver.remote.command.Command.ADD_VIRTUAL_AUTHENTICATOR", "name": "ADD_VIRTUAL_AUTHENTICATOR", "type": "builtins.str"}}, "CLEAR_ELEMENT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "selenium.webdriver.remote.command.Command.CLEAR_ELEMENT", "name": "CLEAR_ELEMENT", "type": "builtins.str"}}, "CLICK_ELEMENT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "selenium.webdriver.remote.command.Command.CLICK_ELEMENT", "name": "CLICK_ELEMENT", "type": "builtins.str"}}, "CLOSE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "selenium.webdriver.remote.command.Command.CLOSE", "name": "CLOSE", "type": "builtins.str"}}, "CONTEXT_HANDLES": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "selenium.webdriver.remote.command.Command.CONTEXT_HANDLES", "name": "CONTEXT_HANDLES", "type": "builtins.str"}}, "CURRENT_CONTEXT_HANDLE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "selenium.webdriver.remote.command.Command.CURRENT_CONTEXT_HANDLE", "name": "CURRENT_CONTEXT_HANDLE", "type": "builtins.str"}}, "DELETE_ALL_COOKIES": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "selenium.webdriver.remote.command.Command.DELETE_ALL_COOKIES", "name": "DELETE_ALL_COOKIES", "type": "builtins.str"}}, "DELETE_COOKIE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "selenium.webdriver.remote.command.Command.DELETE_COOKIE", "name": "DELETE_COOKIE", "type": "builtins.str"}}, "DELETE_DOWNLOADABLE_FILES": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "selenium.webdriver.remote.command.Command.DELETE_DOWNLOADABLE_FILES", "name": "DELETE_DOWNLOADABLE_FILES", "type": "builtins.str"}}, "DELETE_SESSION": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "selenium.webdriver.remote.command.Command.DELETE_SESSION", "name": "DELETE_SESSION", "type": "builtins.str"}}, "DOWNLOAD_FILE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "selenium.webdriver.remote.command.Command.DOWNLOAD_FILE", "name": "DOWNLOAD_FILE", "type": "builtins.str"}}, "ELEMENT_SCREENSHOT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "selenium.webdriver.remote.command.Command.ELEMENT_SCREENSHOT", "name": "ELEMENT_SCREENSHOT", "type": "builtins.str"}}, "EXECUTE_ASYNC_SCRIPT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "selenium.webdriver.remote.command.Command.EXECUTE_ASYNC_SCRIPT", "name": "EXECUTE_ASYNC_SCRIPT", "type": "builtins.str"}}, "FIND_CHILD_ELEMENT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "selenium.webdriver.remote.command.Command.FIND_CHILD_ELEMENT", "name": "FIND_CHILD_ELEMENT", "type": "builtins.str"}}, "FIND_CHILD_ELEMENTS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "selenium.webdriver.remote.command.Command.FIND_CHILD_ELEMENTS", "name": "FIND_CHILD_ELEMENTS", "type": "builtins.str"}}, "FIND_ELEMENT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "selenium.webdriver.remote.command.Command.FIND_ELEMENT", "name": "FIND_ELEMENT", "type": "builtins.str"}}, "FIND_ELEMENTS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "selenium.webdriver.remote.command.Command.FIND_ELEMENTS", "name": "FIND_ELEMENTS", "type": "builtins.str"}}, "FIND_ELEMENTS_FROM_SHADOW_ROOT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "selenium.webdriver.remote.command.Command.FIND_ELEMENTS_FROM_SHADOW_ROOT", "name": "FIND_ELEMENTS_FROM_SHADOW_ROOT", "type": "builtins.str"}}, "FIND_ELEMENT_FROM_SHADOW_ROOT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "selenium.webdriver.remote.command.Command.FIND_ELEMENT_FROM_SHADOW_ROOT", "name": "FIND_ELEMENT_FROM_SHADOW_ROOT", "type": "builtins.str"}}, "FULLSCREEN_WINDOW": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "selenium.webdriver.remote.command.Command.FULLSCREEN_WINDOW", "name": "FULLSCREEN_WINDOW", "type": "builtins.str"}}, "GET": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "selenium.webdriver.remote.command.Command.GET", "name": "GET", "type": "builtins.str"}}, "GET_ALL_COOKIES": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "selenium.webdriver.remote.command.Command.GET_ALL_COOKIES", "name": "GET_ALL_COOKIES", "type": "builtins.str"}}, "GET_AVAILABLE_LOG_TYPES": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "selenium.webdriver.remote.command.Command.GET_AVAILABLE_LOG_TYPES", "name": "GET_AVAILABLE_LOG_TYPES", "type": "builtins.str"}}, "GET_COOKIE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "selenium.webdriver.remote.command.Command.GET_COOKIE", "name": "GET_COOKIE", "type": "builtins.str"}}, "GET_CREDENTIALS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "selenium.webdriver.remote.command.Command.GET_CREDENTIALS", "name": "GET_CREDENTIALS", "type": "builtins.str"}}, "GET_CURRENT_URL": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "selenium.webdriver.remote.command.Command.GET_CURRENT_URL", "name": "GET_CURRENT_URL", "type": "builtins.str"}}, "GET_DOWNLOADABLE_FILES": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "selenium.webdriver.remote.command.Command.GET_DOWNLOADABLE_FILES", "name": "GET_DOWNLOADABLE_FILES", "type": "builtins.str"}}, "GET_ELEMENT_ARIA_LABEL": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "selenium.webdriver.remote.command.Command.GET_ELEMENT_ARIA_LABEL", "name": "GET_ELEMENT_ARIA_LABEL", "type": "builtins.str"}}, "GET_ELEMENT_ARIA_ROLE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "selenium.webdriver.remote.command.Command.GET_ELEMENT_ARIA_ROLE", "name": "GET_ELEMENT_ARIA_ROLE", "type": "builtins.str"}}, "GET_ELEMENT_ATTRIBUTE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "selenium.webdriver.remote.command.Command.GET_ELEMENT_ATTRIBUTE", "name": "GET_ELEMENT_ATTRIBUTE", "type": "builtins.str"}}, "GET_ELEMENT_PROPERTY": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "selenium.webdriver.remote.command.Command.GET_ELEMENT_PROPERTY", "name": "GET_ELEMENT_PROPERTY", "type": "builtins.str"}}, "GET_ELEMENT_RECT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "selenium.webdriver.remote.command.Command.GET_ELEMENT_RECT", "name": "GET_ELEMENT_RECT", "type": "builtins.str"}}, "GET_ELEMENT_TAG_NAME": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "selenium.webdriver.remote.command.Command.GET_ELEMENT_TAG_NAME", "name": "GET_ELEMENT_TAG_NAME", "type": "builtins.str"}}, "GET_ELEMENT_TEXT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "selenium.webdriver.remote.command.Command.GET_ELEMENT_TEXT", "name": "GET_ELEMENT_TEXT", "type": "builtins.str"}}, "GET_ELEMENT_VALUE_OF_CSS_PROPERTY": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "selenium.webdriver.remote.command.Command.GET_ELEMENT_VALUE_OF_CSS_PROPERTY", "name": "GET_ELEMENT_VALUE_OF_CSS_PROPERTY", "type": "builtins.str"}}, "GET_LOG": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "selenium.webdriver.remote.command.Command.GET_LOG", "name": "GET_LOG", "type": "builtins.str"}}, "GET_NETWORK_CONNECTION": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "selenium.webdriver.remote.command.Command.GET_NETWORK_CONNECTION", "name": "GET_NETWORK_CONNECTION", "type": "builtins.str"}}, "GET_PAGE_SOURCE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "selenium.webdriver.remote.command.Command.GET_PAGE_SOURCE", "name": "GET_PAGE_SOURCE", "type": "builtins.str"}}, "GET_SCREEN_ORIENTATION": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "selenium.webdriver.remote.command.Command.GET_SCREEN_ORIENTATION", "name": "GET_SCREEN_ORIENTATION", "type": "builtins.str"}}, "GET_SHADOW_ROOT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "selenium.webdriver.remote.command.Command.GET_SHADOW_ROOT", "name": "GET_SHADOW_ROOT", "type": "builtins.str"}}, "GET_TIMEOUTS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "selenium.webdriver.remote.command.Command.GET_TIMEOUTS", "name": "GET_TIMEOUTS", "type": "builtins.str"}}, "GET_TITLE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "selenium.webdriver.remote.command.Command.GET_TITLE", "name": "GET_TITLE", "type": "builtins.str"}}, "GET_WINDOW_RECT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "selenium.webdriver.remote.command.Command.GET_WINDOW_RECT", "name": "GET_WINDOW_RECT", "type": "builtins.str"}}, "GO_BACK": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "selenium.webdriver.remote.command.Command.GO_BACK", "name": "GO_BACK", "type": "builtins.str"}}, "GO_FORWARD": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "selenium.webdriver.remote.command.Command.GO_FORWARD", "name": "GO_FORWARD", "type": "builtins.str"}}, "IS_ELEMENT_ENABLED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "selenium.webdriver.remote.command.Command.IS_ELEMENT_ENABLED", "name": "IS_ELEMENT_ENABLED", "type": "builtins.str"}}, "IS_ELEMENT_SELECTED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "selenium.webdriver.remote.command.Command.IS_ELEMENT_SELECTED", "name": "IS_ELEMENT_SELECTED", "type": "builtins.str"}}, "MINIMIZE_WINDOW": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "selenium.webdriver.remote.command.Command.MINIMIZE_WINDOW", "name": "MINIMIZE_WINDOW", "type": "builtins.str"}}, "NEW_SESSION": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "selenium.webdriver.remote.command.Command.NEW_SESSION", "name": "NEW_SESSION", "type": "builtins.str"}}, "NEW_WINDOW": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "selenium.webdriver.remote.command.Command.NEW_WINDOW", "name": "NEW_WINDOW", "type": "builtins.str"}}, "PRINT_PAGE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "selenium.webdriver.remote.command.Command.PRINT_PAGE", "name": "PRINT_PAGE", "type": "builtins.str"}}, "QUIT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "selenium.webdriver.remote.command.Command.QUIT", "name": "QUIT", "type": "builtins.str"}}, "REFRESH": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "selenium.webdriver.remote.command.Command.REFRESH", "name": "REFRESH", "type": "builtins.str"}}, "REMOVE_ALL_CREDENTIALS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "selenium.webdriver.remote.command.Command.REMOVE_ALL_CREDENTIALS", "name": "REMOVE_ALL_CREDENTIALS", "type": "builtins.str"}}, "REMOVE_CREDENTIAL": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "selenium.webdriver.remote.command.Command.REMOVE_CREDENTIAL", "name": "REMOVE_CREDENTIAL", "type": "builtins.str"}}, "REMOVE_VIRTUAL_AUTHENTICATOR": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "selenium.webdriver.remote.command.Command.REMOVE_VIRTUAL_AUTHENTICATOR", "name": "REMOVE_VIRTUAL_AUTHENTICATOR", "type": "builtins.str"}}, "SCREENSHOT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "selenium.webdriver.remote.command.Command.SCREENSHOT", "name": "SCREENSHOT", "type": "builtins.str"}}, "SEND_KEYS_TO_ELEMENT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "selenium.webdriver.remote.command.Command.SEND_KEYS_TO_ELEMENT", "name": "SEND_KEYS_TO_ELEMENT", "type": "builtins.str"}}, "SET_NETWORK_CONNECTION": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "selenium.webdriver.remote.command.Command.SET_NETWORK_CONNECTION", "name": "SET_NETWORK_CONNECTION", "type": "builtins.str"}}, "SET_SCREEN_ORIENTATION": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "selenium.webdriver.remote.command.Command.SET_SCREEN_ORIENTATION", "name": "SET_SCREEN_ORIENTATION", "type": "builtins.str"}}, "SET_TIMEOUTS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "selenium.webdriver.remote.command.Command.SET_TIMEOUTS", "name": "SET_TIMEOUTS", "type": "builtins.str"}}, "SET_USER_VERIFIED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "selenium.webdriver.remote.command.Command.SET_USER_VERIFIED", "name": "SET_USER_VERIFIED", "type": "builtins.str"}}, "SET_WINDOW_RECT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "selenium.webdriver.remote.command.Command.SET_WINDOW_RECT", "name": "SET_WINDOW_RECT", "type": "builtins.str"}}, "SWITCH_TO_CONTEXT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "selenium.webdriver.remote.command.Command.SWITCH_TO_CONTEXT", "name": "SWITCH_TO_CONTEXT", "type": "builtins.str"}}, "SWITCH_TO_FRAME": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "selenium.webdriver.remote.command.Command.SWITCH_TO_FRAME", "name": "SWITCH_TO_FRAME", "type": "builtins.str"}}, "SWITCH_TO_PARENT_FRAME": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "selenium.webdriver.remote.command.Command.SWITCH_TO_PARENT_FRAME", "name": "SWITCH_TO_PARENT_FRAME", "type": "builtins.str"}}, "SWITCH_TO_WINDOW": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "selenium.webdriver.remote.command.Command.SWITCH_TO_WINDOW", "name": "SWITCH_TO_WINDOW", "type": "builtins.str"}}, "UPLOAD_FILE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "selenium.webdriver.remote.command.Command.UPLOAD_FILE", "name": "UPLOAD_FILE", "type": "builtins.str"}}, "W3C_ACCEPT_ALERT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "selenium.webdriver.remote.command.Command.W3C_ACCEPT_ALERT", "name": "W3C_ACCEPT_ALERT", "type": "builtins.str"}}, "W3C_ACTIONS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "selenium.webdriver.remote.command.Command.W3C_ACTIONS", "name": "W3C_ACTIONS", "type": "builtins.str"}}, "W3C_CLEAR_ACTIONS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "selenium.webdriver.remote.command.Command.W3C_CLEAR_ACTIONS", "name": "W3C_CLEAR_ACTIONS", "type": "builtins.str"}}, "W3C_DISMISS_ALERT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "selenium.webdriver.remote.command.Command.W3C_DISMISS_ALERT", "name": "W3C_DISMISS_ALERT", "type": "builtins.str"}}, "W3C_EXECUTE_SCRIPT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "selenium.webdriver.remote.command.Command.W3C_EXECUTE_SCRIPT", "name": "W3C_EXECUTE_SCRIPT", "type": "builtins.str"}}, "W3C_EXECUTE_SCRIPT_ASYNC": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "selenium.webdriver.remote.command.Command.W3C_EXECUTE_SCRIPT_ASYNC", "name": "W3C_EXECUTE_SCRIPT_ASYNC", "type": "builtins.str"}}, "W3C_GET_ACTIVE_ELEMENT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "selenium.webdriver.remote.command.Command.W3C_GET_ACTIVE_ELEMENT", "name": "W3C_GET_ACTIVE_ELEMENT", "type": "builtins.str"}}, "W3C_GET_ALERT_TEXT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "selenium.webdriver.remote.command.Command.W3C_GET_ALERT_TEXT", "name": "W3C_GET_ALERT_TEXT", "type": "builtins.str"}}, "W3C_GET_CURRENT_WINDOW_HANDLE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "selenium.webdriver.remote.command.Command.W3C_GET_CURRENT_WINDOW_HANDLE", "name": "W3C_GET_CURRENT_WINDOW_HANDLE", "type": "builtins.str"}}, "W3C_GET_WINDOW_HANDLES": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "selenium.webdriver.remote.command.Command.W3C_GET_WINDOW_HANDLES", "name": "W3C_GET_WINDOW_HANDLES", "type": "builtins.str"}}, "W3C_MAXIMIZE_WINDOW": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "selenium.webdriver.remote.command.Command.W3C_MAXIMIZE_WINDOW", "name": "W3C_MAXIMIZE_WINDOW", "type": "builtins.str"}}, "W3C_SET_ALERT_VALUE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "selenium.webdriver.remote.command.Command.W3C_SET_ALERT_VALUE", "name": "W3C_SET_ALERT_VALUE", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "selenium.webdriver.remote.command.Command.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "selenium.webdriver.remote.command.Command", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "selenium.webdriver.remote.command.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "selenium.webdriver.remote.command.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "selenium.webdriver.remote.command.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "selenium.webdriver.remote.command.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "selenium.webdriver.remote.command.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "selenium.webdriver.remote.command.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\selenium\\webdriver\\remote\\command.py"}