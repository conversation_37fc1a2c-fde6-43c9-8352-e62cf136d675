{"data_mtime": 1757882372, "dep_lines": [2, 3, 4, 5, 6, 6, 1, 7, 1, 8, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 10, 20, 10, 5, 20, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["selenium.webdriver.chrome.service", "selenium.webdriver.chrome.options", "selenium.webdriver.common.by", "selenium.webdriver.support.ui", "selenium.webdriver.support.expected_conditions", "selenium.webdriver.support", "selenium.webdriver", "webdriver_manager.chrome", "selenium", "time", "builtins", "_frozen_importlib", "_typeshed", "abc", "selenium.webdriver.chrome", "selenium.webdriver.chrome.webdriver", "selenium.webdriver.chromium", "selenium.webdriver.chromium.options", "selenium.webdriver.chromium.service", "selenium.webdriver.chromium.webdriver", "selenium.webdriver.common", "selenium.webdriver.common.options", "selenium.webdriver.common.service", "selenium.webdriver.remote", "selenium.webdriver.remote.webdriver", "selenium.webdriver.remote.webelement", "selenium.webdriver.support.wait", "typing", "webdriver_manager", "webdriver_manager.core", "webdriver_manager.core.download_manager", "webdriver_manager.core.driver_cache", "webdriver_manager.core.manager", "webdriver_manager.core.os_manager"], "hash": "6bc0abfed922670d1edad76a12575c45fbbca696", "id": "google_selenium_auto", "ignore_all": false, "interface_hash": "54e45271a524ff7bbc93fd39b7720915e432842e", "mtime": 1757882675, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Documents\\python\\chomikuj\\google_selenium_auto.py", "plugin_data": null, "size": 2410, "suppressed": [], "version_id": "1.15.0"}