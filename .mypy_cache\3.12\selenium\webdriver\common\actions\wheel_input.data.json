{".class": "MypyFile", "_fullname": "selenium.webdriver.common.actions.wheel_input", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "InputDevice": {".class": "SymbolTableNode", "cross_ref": "selenium.webdriver.common.actions.input_device.InputDevice", "kind": "Gdef"}, "ScrollOrigin": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "selenium.webdriver.common.actions.wheel_input.ScrollOrigin", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "selenium.webdriver.common.actions.wheel_input.ScrollOrigin", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "selenium.webdriver.common.actions.wheel_input", "mro": ["selenium.webdriver.common.actions.wheel_input.ScrollOrigin", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "origin", "x_offset", "y_offset"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "selenium.webdriver.common.actions.wheel_input.ScrollOrigin.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "origin", "x_offset", "y_offset"], "arg_types": ["selenium.webdriver.common.actions.wheel_input.ScrollOrigin", {".class": "UnionType", "items": ["builtins.str", "selenium.webdriver.remote.webelement.WebElement"], "uses_pep604_syntax": false}, "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ScrollOrigin", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_origin": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "selenium.webdriver.common.actions.wheel_input.ScrollOrigin._origin", "name": "_origin", "type": {".class": "UnionType", "items": ["builtins.str", "selenium.webdriver.remote.webelement.WebElement"], "uses_pep604_syntax": false}}}, "_x_offset": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "selenium.webdriver.common.actions.wheel_input.ScrollOrigin._x_offset", "name": "_x_offset", "type": "builtins.int"}}, "_y_offset": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "selenium.webdriver.common.actions.wheel_input.ScrollOrigin._y_offset", "name": "_y_offset", "type": "builtins.int"}}, "from_element": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["cls", "element", "x_offset", "y_offset"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "selenium.webdriver.common.actions.wheel_input.ScrollOrigin.from_element", "name": "from_element", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["cls", "element", "x_offset", "y_offset"], "arg_types": [{".class": "TypeType", "item": "selenium.webdriver.common.actions.wheel_input.ScrollOrigin"}, "selenium.webdriver.remote.webelement.WebElement", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_element of ScrollOrigin", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "selenium.webdriver.common.actions.wheel_input.ScrollOrigin.from_element", "name": "from_element", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["cls", "element", "x_offset", "y_offset"], "arg_types": [{".class": "TypeType", "item": "selenium.webdriver.common.actions.wheel_input.ScrollOrigin"}, "selenium.webdriver.remote.webelement.WebElement", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_element of ScrollOrigin", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "from_viewport": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["cls", "x_offset", "y_offset"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "selenium.webdriver.common.actions.wheel_input.ScrollOrigin.from_viewport", "name": "from_viewport", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["cls", "x_offset", "y_offset"], "arg_types": [{".class": "TypeType", "item": "selenium.webdriver.common.actions.wheel_input.ScrollOrigin"}, "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_viewport of ScrollOrigin", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "selenium.webdriver.common.actions.wheel_input.ScrollOrigin.from_viewport", "name": "from_viewport", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["cls", "x_offset", "y_offset"], "arg_types": [{".class": "TypeType", "item": "selenium.webdriver.common.actions.wheel_input.ScrollOrigin"}, "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_viewport of ScrollOrigin", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "origin": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "selenium.webdriver.common.actions.wheel_input.ScrollOrigin.origin", "name": "origin", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["selenium.webdriver.common.actions.wheel_input.ScrollOrigin"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "origin of ScrollOrigin", "ret_type": {".class": "UnionType", "items": ["builtins.str", "selenium.webdriver.remote.webelement.WebElement"], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "selenium.webdriver.common.actions.wheel_input.ScrollOrigin.origin", "name": "origin", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["selenium.webdriver.common.actions.wheel_input.ScrollOrigin"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "origin of ScrollOrigin", "ret_type": {".class": "UnionType", "items": ["builtins.str", "selenium.webdriver.remote.webelement.WebElement"], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "x_offset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "selenium.webdriver.common.actions.wheel_input.ScrollOrigin.x_offset", "name": "x_offset", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["selenium.webdriver.common.actions.wheel_input.ScrollOrigin"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "x_offset of ScrollOrigin", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "selenium.webdriver.common.actions.wheel_input.ScrollOrigin.x_offset", "name": "x_offset", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["selenium.webdriver.common.actions.wheel_input.ScrollOrigin"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "x_offset of ScrollOrigin", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "y_offset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "selenium.webdriver.common.actions.wheel_input.ScrollOrigin.y_offset", "name": "y_offset", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["selenium.webdriver.common.actions.wheel_input.ScrollOrigin"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "y_offset of ScrollOrigin", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "selenium.webdriver.common.actions.wheel_input.ScrollOrigin.y_offset", "name": "y_offset", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["selenium.webdriver.common.actions.wheel_input.ScrollOrigin"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "y_offset of ScrollOrigin", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "selenium.webdriver.common.actions.wheel_input.ScrollOrigin.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "selenium.webdriver.common.actions.wheel_input.ScrollOrigin", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "WebElement": {".class": "SymbolTableNode", "cross_ref": "selenium.webdriver.remote.webelement.WebElement", "kind": "Gdef"}, "WheelInput": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["selenium.webdriver.common.actions.input_device.InputDevice"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "selenium.webdriver.common.actions.wheel_input.WheelInput", "name": "WheelInput", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "selenium.webdriver.common.actions.wheel_input.WheelInput", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "selenium.webdriver.common.actions.wheel_input", "mro": ["selenium.webdriver.common.actions.wheel_input.WheelInput", "selenium.webdriver.common.actions.input_device.InputDevice", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "selenium.webdriver.common.actions.wheel_input.WheelInput.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "name"], "arg_types": ["selenium.webdriver.common.actions.wheel_input.WheelInput", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of WheelInput", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_pause": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "pause_duration"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "selenium.webdriver.common.actions.wheel_input.WheelInput.create_pause", "name": "create_pause", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "pause_duration"], "arg_types": ["selenium.webdriver.common.actions.wheel_input.WheelInput", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_pause of WheelInput", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_scroll": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "x", "y", "delta_x", "delta_y", "duration", "origin"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "selenium.webdriver.common.actions.wheel_input.WheelInput.create_scroll", "name": "create_scroll", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "x", "y", "delta_x", "delta_y", "duration", "origin"], "arg_types": ["selenium.webdriver.common.actions.wheel_input.WheelInput", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_scroll of WheelInput", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "encode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "selenium.webdriver.common.actions.wheel_input.WheelInput.encode", "name": "encode", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["selenium.webdriver.common.actions.wheel_input.WheelInput"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "encode of WheelInput", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "type": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "selenium.webdriver.common.actions.wheel_input.WheelInput.type", "name": "type", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "selenium.webdriver.common.actions.wheel_input.WheelInput.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "selenium.webdriver.common.actions.wheel_input.WheelInput", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "selenium.webdriver.common.actions.wheel_input.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "selenium.webdriver.common.actions.wheel_input.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "selenium.webdriver.common.actions.wheel_input.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "selenium.webdriver.common.actions.wheel_input.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "selenium.webdriver.common.actions.wheel_input.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "selenium.webdriver.common.actions.wheel_input.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "interaction": {".class": "SymbolTableNode", "cross_ref": "selenium.webdriver.common.actions.interaction", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\selenium\\webdriver\\common\\actions\\wheel_input.py"}