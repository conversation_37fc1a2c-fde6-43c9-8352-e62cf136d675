{".class": "MypyFile", "_fullname": "xml.sax.handler", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "ContentHandler": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "xml.sax.handler.ContentHandler", "name": "ContentHandler", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "xml.sax.handler.ContentHandler", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "xml.sax.handler", "mro": ["xml.sax.handler.ContentHandler", "builtins.object"], "names": {".class": "SymbolTable", "characters": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "content"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "xml.sax.handler.ContentHandler.characters", "name": "characters", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "content"], "arg_types": ["xml.sax.handler.ContentHandler", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "characters of <PERSON><PERSON><PERSON><PERSON>", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "endDocument": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "xml.sax.handler.ContentHandler.endDocument", "name": "endDocument", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["xml.sax.handler.ContentHandler"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "endDocument of ContentHandler", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "endElement": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "xml.sax.handler.ContentHandler.endElement", "name": "endElement", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "name"], "arg_types": ["xml.sax.handler.ContentHandler", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "endElement of ContentHandler", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "endElementNS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "qname"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "xml.sax.handler.ContentHandler.endElementNS", "name": "endElementNS", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "qname"], "arg_types": ["xml.sax.handler.ContentHandler", {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "endElementNS of ContentHandler", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "endPrefixMapping": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "prefix"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "xml.sax.handler.ContentHandler.endPrefixMapping", "name": "endPrefixMapping", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "prefix"], "arg_types": ["xml.sax.handler.ContentHandler", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "endPrefixMapping of ContentHandler", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ignorableWhitespace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "whitespace"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "xml.sax.handler.ContentHandler.ignorableWhitespace", "name": "ignorableWhitespace", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "whitespace"], "arg_types": ["xml.sax.handler.ContentHandler", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ignorableWhitespace of ContentHandler", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "processingInstruction": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "target", "data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "xml.sax.handler.ContentHandler.processingInstruction", "name": "processingInstruction", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "target", "data"], "arg_types": ["xml.sax.handler.ContentHandler", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "processingInstruction of ContentHandler", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "setDocumentLocator": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "locator"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "xml.sax.handler.ContentHandler.setDocumentLocator", "name": "setDocumentLocator", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "locator"], "arg_types": ["xml.sax.handler.ContentHandler", "xml.sax.xmlreader.Locator"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setDocumentLocator of ContentHandler", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "skippedEntity": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "xml.sax.handler.ContentHandler.skippedEntity", "name": "skippedEntity", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "name"], "arg_types": ["xml.sax.handler.ContentHandler", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "skippedEntity of ContentHandler", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "startDocument": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "xml.sax.handler.ContentHandler.startDocument", "name": "startDocument", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["xml.sax.handler.ContentHandler"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "startDocument of ContentHandler", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "startElement": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "attrs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "xml.sax.handler.ContentHandler.startElement", "name": "startElement", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "attrs"], "arg_types": ["xml.sax.handler.ContentHandler", "builtins.str", "xml.sax.xmlreader.AttributesImpl"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "startElement of ContentHandler", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "startElementNS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "name", "qname", "attrs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "xml.sax.handler.ContentHandler.startElementNS", "name": "startElementNS", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "name", "qname", "attrs"], "arg_types": ["xml.sax.handler.ContentHandler", {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "builtins.str", "xml.sax.xmlreader.AttributesNSImpl"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "startElementNS of ContentHandler", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "startPrefixMapping": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "prefix", "uri"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "xml.sax.handler.ContentHandler.startPrefixMapping", "name": "startPrefixMapping", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "prefix", "uri"], "arg_types": ["xml.sax.handler.ContentHandler", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "startPrefixMapping of ContentHandler", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "xml.sax.handler.ContentHandler.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "xml.sax.handler.ContentHandler", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DTDHandler": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "xml.sax.handler.DTDHandler", "name": "DTDHandler", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "xml.sax.handler.DTDHandler", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "xml.sax.handler", "mro": ["xml.sax.handler.DTDHandler", "builtins.object"], "names": {".class": "SymbolTable", "notationDecl": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "name", "publicId", "systemId"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "xml.sax.handler.DTDHandler.notationDecl", "name": "notationDecl", "type": null}}, "unparsedEntityDecl": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "name", "publicId", "systemId", "ndata"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "xml.sax.handler.DTDHandler.unparsedEntityDecl", "name": "unparsedEntityDecl", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "xml.sax.handler.DTDHandler.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "xml.sax.handler.DTDHandler", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "EntityResolver": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "xml.sax.handler.EntityResolver", "name": "EntityResolver", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "xml.sax.handler.EntityResolver", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "xml.sax.handler", "mro": ["xml.sax.handler.EntityResolver", "builtins.object"], "names": {".class": "SymbolTable", "resolveEntity": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "publicId", "systemId"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "xml.sax.handler.EntityResolver.resolveEntity", "name": "resolveEntity", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "xml.sax.handler.EntityResolver.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "xml.sax.handler.EntityResolver", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ErrorHandler": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "xml.sax.handler.ErrorHandler", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "xml.sax.handler.ErrorHandler", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "xml.sax.handler", "mro": ["xml.sax.handler.ErrorHandler", "builtins.object"], "names": {".class": "SymbolTable", "error": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "exception"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "xml.sax.handler.ErrorHandler.error", "name": "error", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "exception"], "arg_types": ["xml.sax.handler.ErrorHandler", "builtins.BaseException"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "error of <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ret_type": {".class": "UninhabitedType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "fatalError": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "exception"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "xml.sax.handler.ErrorHandler.fatalError", "name": "fatalError", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "exception"], "arg_types": ["xml.sax.handler.ErrorHandler", "builtins.BaseException"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fatalError of ErrorHandler", "ret_type": {".class": "UninhabitedType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "warning": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "exception"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "xml.sax.handler.ErrorHandler.warning", "name": "warning", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "exception"], "arg_types": ["xml.sax.handler.ErrorHandler", "builtins.BaseException"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "warning of <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "xml.sax.handler.ErrorHandler.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "xml.sax.handler.ErrorHandler", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "LexicalHandler": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "xml.sax.handler.LexicalHandler", "name": "LexicalHandler", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "xml.sax.handler.LexicalHandler", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "xml.sax.handler", "mro": ["xml.sax.handler.LexicalHandler", "builtins.object"], "names": {".class": "SymbolTable", "comment": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "content"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "xml.sax.handler.LexicalHandler.comment", "name": "comment", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "content"], "arg_types": ["xml.sax.handler.LexicalHandler", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "comment of <PERSON><PERSON><PERSON><PERSON><PERSON>", "ret_type": "builtins.object", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "endCDATA": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "xml.sax.handler.LexicalHandler.endCDATA", "name": "endCDATA", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["xml.sax.handler.LexicalHandler"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "endCDATA of LexicalHandler", "ret_type": "builtins.object", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "endDTD": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "xml.sax.handler.LexicalHandler.endDTD", "name": "endDTD", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["xml.sax.handler.LexicalHandler"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "endDTD of LexicalHandler", "ret_type": "builtins.object", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "startCDATA": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "xml.sax.handler.LexicalHandler.startCDATA", "name": "startCDATA", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["xml.sax.handler.LexicalHandler"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "startCDATA of LexicalHandler", "ret_type": "builtins.object", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "startDTD": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "name", "public_id", "system_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "xml.sax.handler.LexicalHandler.startDTD", "name": "startDTD", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "name", "public_id", "system_id"], "arg_types": ["xml.sax.handler.LexicalHandler", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "startDTD of LexicalHandler", "ret_type": "builtins.object", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "xml.sax.handler.LexicalHandler.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "xml.sax.handler.LexicalHandler", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NoReturn": {".class": "SymbolTableNode", "cross_ref": "typing.NoReturn", "kind": "Gdef", "module_hidden": true, "module_public": false}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "xml.sax.handler.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "xml.sax.handler.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "xml.sax.handler.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "xml.sax.handler.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "xml.sax.handler.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "xml.sax.handler.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "all_features": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "xml.sax.handler.all_features", "name": "all_features", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "all_properties": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "xml.sax.handler.all_properties", "name": "all_properties", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "feature_external_ges": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "xml.sax.handler.feature_external_ges", "name": "feature_external_ges", "type": "builtins.str"}}, "feature_external_pes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "xml.sax.handler.feature_external_pes", "name": "feature_external_pes", "type": "builtins.str"}}, "feature_namespace_prefixes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "xml.sax.handler.feature_namespace_prefixes", "name": "feature_namespace_prefixes", "type": "builtins.str"}}, "feature_namespaces": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "xml.sax.handler.feature_namespaces", "name": "feature_namespaces", "type": "builtins.str"}}, "feature_string_interning": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "xml.sax.handler.feature_string_interning", "name": "feature_string_interning", "type": "builtins.str"}}, "feature_validation": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "xml.sax.handler.feature_validation", "name": "feature_validation", "type": "builtins.str"}}, "property_declaration_handler": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "xml.sax.handler.property_declaration_handler", "name": "property_declaration_handler", "type": "builtins.str"}}, "property_dom_node": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "xml.sax.handler.property_dom_node", "name": "property_dom_node", "type": "builtins.str"}}, "property_encoding": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "xml.sax.handler.property_encoding", "name": "property_encoding", "type": "builtins.str"}}, "property_interning_dict": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "xml.sax.handler.property_interning_dict", "name": "property_interning_dict", "type": "builtins.str"}}, "property_lexical_handler": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "xml.sax.handler.property_lexical_handler", "name": "property_lexical_handler", "type": "builtins.str"}}, "property_xml_string": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "xml.sax.handler.property_xml_string", "name": "property_xml_string", "type": "builtins.str"}}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_hidden": true, "module_public": false}, "version": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "xml.sax.handler.version", "name": "version", "type": "builtins.str"}}, "xmlreader": {".class": "SymbolTableNode", "cross_ref": "xml.sax.xmlreader", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.mypy-type-checker-2025.2.0\\bundled\\libs\\mypy\\typeshed\\stdlib\\xml\\sax\\handler.pyi"}