{".class": "MypyFile", "_fullname": "webdriver_manager.core.manager", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "DriverCacheManager": {".class": "SymbolTableNode", "cross_ref": "webdriver_manager.core.driver_cache.DriverCacheManager", "kind": "Gdef"}, "DriverManager": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "webdriver_manager.core.manager.DriverManager", "name": "<PERSON><PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "webdriver_manager.core.manager.DriverManager", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "webdriver_manager.core.manager", "mro": ["webdriver_manager.core.manager.DriverManager", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "download_manager", "cache_manager", "os_system_manager"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "webdriver_manager.core.manager.DriverManager.__init__", "name": "__init__", "type": null}}, "_cache_manager": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "webdriver_manager.core.manager.DriverManager._cache_manager", "name": "_cache_manager", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_download_manager": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "webdriver_manager.core.manager.DriverManager._download_manager", "name": "_download_manager", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_get_driver_binary_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "driver"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "webdriver_manager.core.manager.DriverManager._get_driver_binary_path", "name": "_get_driver_binary_path", "type": null}}, "_os_system_manager": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "webdriver_manager.core.manager.DriverManager._os_system_manager", "name": "_os_system_manager", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "get_os_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "webdriver_manager.core.manager.DriverManager.get_os_type", "name": "get_os_type", "type": null}}, "http_client": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "webdriver_manager.core.manager.DriverManager.http_client", "name": "http_client", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "webdriver_manager.core.manager.DriverManager.http_client", "name": "http_client", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["webdriver_manager.core.manager.DriverManager"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "http_client of DriverManager", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "install": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "webdriver_manager.core.manager.DriverManager.install", "name": "install", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["webdriver_manager.core.manager.DriverManager"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "install of DriverManager", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "webdriver_manager.core.manager.DriverManager.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "webdriver_manager.core.manager.DriverManager", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "OperationSystemManager": {".class": "SymbolTableNode", "cross_ref": "webdriver_manager.core.os_manager.OperationSystemManager", "kind": "Gdef"}, "WDMDownloadManager": {".class": "SymbolTableNode", "cross_ref": "webdriver_manager.core.download_manager.WDMDownloadManager", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "webdriver_manager.core.manager.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "webdriver_manager.core.manager.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "webdriver_manager.core.manager.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "webdriver_manager.core.manager.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "webdriver_manager.core.manager.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "webdriver_manager.core.manager.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "log": {".class": "SymbolTableNode", "cross_ref": "webdriver_manager.core.logger.log", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\webdriver_manager\\core\\manager.py"}