{".class": "MypyFile", "_fullname": "selenium.webdriver.common.actions.key_actions", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Interaction": {".class": "SymbolTableNode", "cross_ref": "selenium.webdriver.common.actions.interaction.Interaction", "kind": "Gdef"}, "KEY": {".class": "SymbolTableNode", "cross_ref": "selenium.webdriver.common.actions.interaction.KEY", "kind": "Gdef"}, "KeyActions": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["selenium.webdriver.common.actions.interaction.Interaction"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "selenium.webdriver.common.actions.key_actions.KeyActions", "name": "KeyActions", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "selenium.webdriver.common.actions.key_actions.KeyActions", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "selenium.webdriver.common.actions.key_actions", "mro": ["selenium.webdriver.common.actions.key_actions.KeyActions", "selenium.webdriver.common.actions.interaction.Interaction", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "source"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "selenium.webdriver.common.actions.key_actions.KeyActions.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "source"], "arg_types": ["selenium.webdriver.common.actions.key_actions.KeyActions", {".class": "UnionType", "items": ["selenium.webdriver.common.actions.key_input.KeyInput", "selenium.webdriver.common.actions.pointer_input.PointerInput", "selenium.webdriver.common.actions.wheel_input.WheelInput", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of KeyActions", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_key_action": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "action", "letter"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "selenium.webdriver.common.actions.key_actions.KeyActions._key_action", "name": "_key_action", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "action", "letter"], "arg_types": ["selenium.webdriver.common.actions.key_actions.KeyActions", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_key_action of KeyActions", "ret_type": "selenium.webdriver.common.actions.key_actions.KeyActions", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "key_down": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "letter"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "selenium.webdriver.common.actions.key_actions.KeyActions.key_down", "name": "key_down", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "letter"], "arg_types": ["selenium.webdriver.common.actions.key_actions.KeyActions", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "key_down of KeyActions", "ret_type": "selenium.webdriver.common.actions.key_actions.KeyActions", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "key_up": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "letter"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "selenium.webdriver.common.actions.key_actions.KeyActions.key_up", "name": "key_up", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "letter"], "arg_types": ["selenium.webdriver.common.actions.key_actions.KeyActions", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "key_up of KeyActions", "ret_type": "selenium.webdriver.common.actions.key_actions.KeyActions", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pause": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "duration"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "selenium.webdriver.common.actions.key_actions.KeyActions.pause", "name": "pause", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "duration"], "arg_types": ["selenium.webdriver.common.actions.key_actions.KeyActions", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pause of KeyActions", "ret_type": "selenium.webdriver.common.actions.key_actions.KeyActions", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "send_keys": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "text"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "selenium.webdriver.common.actions.key_actions.KeyActions.send_keys", "name": "send_keys", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "text"], "arg_types": ["selenium.webdriver.common.actions.key_actions.KeyActions", {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "send_keys of KeyActions", "ret_type": "selenium.webdriver.common.actions.key_actions.KeyActions", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "selenium.webdriver.common.actions.key_actions.KeyActions.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "selenium.webdriver.common.actions.key_actions.KeyActions", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "KeyInput": {".class": "SymbolTableNode", "cross_ref": "selenium.webdriver.common.actions.key_input.KeyInput", "kind": "Gdef"}, "PointerInput": {".class": "SymbolTableNode", "cross_ref": "selenium.webdriver.common.actions.pointer_input.PointerInput", "kind": "Gdef"}, "WheelInput": {".class": "SymbolTableNode", "cross_ref": "selenium.webdriver.common.actions.wheel_input.WheelInput", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "selenium.webdriver.common.actions.key_actions.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "selenium.webdriver.common.actions.key_actions.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "selenium.webdriver.common.actions.key_actions.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "selenium.webdriver.common.actions.key_actions.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "selenium.webdriver.common.actions.key_actions.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "selenium.webdriver.common.actions.key_actions.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "keys_to_typing": {".class": "SymbolTableNode", "cross_ref": "selenium.webdriver.common.utils.keys_to_typing", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\selenium\\webdriver\\common\\actions\\key_actions.py"}