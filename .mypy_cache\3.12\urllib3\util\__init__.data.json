{".class": "MypyFile", "_fullname": "urllib3.util", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ALPN_PROTOCOLS": {".class": "SymbolTableNode", "cross_ref": "urllib3.util.ssl_.ALPN_PROTOCOLS", "kind": "Gdef"}, "IS_PYOPENSSL": {".class": "SymbolTableNode", "cross_ref": "urllib3.util.ssl_.IS_PYOPENSSL", "kind": "Gdef"}, "Retry": {".class": "SymbolTableNode", "cross_ref": "urllib3.util.retry.Retry", "kind": "Gdef"}, "SKIPPABLE_HEADERS": {".class": "SymbolTableNode", "cross_ref": "urllib3.util.request.SKIPPABLE_HEADERS", "kind": "Gdef"}, "SKIP_HEADER": {".class": "SymbolTableNode", "cross_ref": "urllib3.util.request.SKIP_HEADER", "kind": "Gdef"}, "SSLContext": {".class": "SymbolTableNode", "cross_ref": "urllib3.util.ssl_.SSLContext", "kind": "Gdef"}, "Timeout": {".class": "SymbolTableNode", "cross_ref": "urllib3.util.timeout.Timeout", "kind": "Gdef"}, "Url": {".class": "SymbolTableNode", "cross_ref": "urllib3.util.url.Url", "kind": "Gdef"}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "urllib3.util.__all__", "name": "__all__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "urllib3.util.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "urllib3.util.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "urllib3.util.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "urllib3.util.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "urllib3.util.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "urllib3.util.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "urllib3.util.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_public": false}, "assert_fingerprint": {".class": "SymbolTableNode", "cross_ref": "urllib3.util.ssl_.assert_fingerprint", "kind": "Gdef"}, "create_urllib3_context": {".class": "SymbolTableNode", "cross_ref": "urllib3.util.ssl_.create_urllib3_context", "kind": "Gdef"}, "is_connection_dropped": {".class": "SymbolTableNode", "cross_ref": "urllib3.util.connection.is_connection_dropped", "kind": "Gdef"}, "is_fp_closed": {".class": "SymbolTableNode", "cross_ref": "urllib3.util.response.is_fp_closed", "kind": "Gdef"}, "make_headers": {".class": "SymbolTableNode", "cross_ref": "urllib3.util.request.make_headers", "kind": "Gdef"}, "parse_url": {".class": "SymbolTableNode", "cross_ref": "urllib3.util.url.parse_url", "kind": "Gdef"}, "resolve_cert_reqs": {".class": "SymbolTableNode", "cross_ref": "urllib3.util.ssl_.resolve_cert_reqs", "kind": "Gdef"}, "resolve_ssl_version": {".class": "SymbolTableNode", "cross_ref": "urllib3.util.ssl_.resolve_ssl_version", "kind": "Gdef"}, "ssl_wrap_socket": {".class": "SymbolTableNode", "cross_ref": "urllib3.util.ssl_.ssl_wrap_socket", "kind": "Gdef"}, "wait_for_read": {".class": "SymbolTableNode", "cross_ref": "urllib3.util.wait.wait_for_read", "kind": "Gdef"}, "wait_for_write": {".class": "SymbolTableNode", "cross_ref": "urllib3.util.wait.wait_for_write", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\urllib3\\util\\__init__.py"}