{"data_mtime": 1757882371, "dep_lines": [17, 18, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 30, 30, 30, 30], "dependencies": ["selenium.webdriver.common.actions.interaction", "selenium.webdriver.common.actions.wheel_input", "builtins", "_frozen_importlib", "abc", "selenium.webdriver.common.actions.input_device", "typing"], "hash": "94a02823d0d3eb812313e12d600dd9d612a2c0ab", "id": "selenium.webdriver.common.actions.wheel_actions", "ignore_all": true, "interface_hash": "dc7d2cd0ef9b81bf12275614cba794d56f6b555b", "mtime": 1757882171, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\selenium\\webdriver\\common\\actions\\wheel_actions.py", "plugin_data": null, "size": 1331, "suppressed": [], "version_id": "1.15.0"}