{".class": "MypyFile", "_fullname": "webdriver_manager.drivers.chrome", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ChromeDriver": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["webdriver_manager.core.driver.Driver"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "webdriver_manager.drivers.chrome.ChromeDriver", "name": "ChromeDriver", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "webdriver_manager.drivers.chrome.ChromeDriver", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "webdriver_manager.drivers.chrome", "mro": ["webdriver_manager.drivers.chrome.ChromeDriver", "webdriver_manager.core.driver.Driver", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 1], "arg_names": ["self", "name", "driver_version", "url", "latest_release_url", "http_client", "os_system_manager", "chrome_type"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "webdriver_manager.drivers.chrome.ChromeDriver.__init__", "name": "__init__", "type": null}}, "_browser_type": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "webdriver_manager.drivers.chrome.ChromeDriver._browser_type", "name": "_browser_type", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "get_browser_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "webdriver_manager.drivers.chrome.ChromeDriver.get_browser_type", "name": "get_browser_type", "type": null}}, "get_driver_download_url": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "os_type"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "webdriver_manager.drivers.chrome.ChromeDriver.get_driver_download_url", "name": "get_driver_download_url", "type": null}}, "get_latest_release_version": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "webdriver_manager.drivers.chrome.ChromeDriver.get_latest_release_version", "name": "get_latest_release_version", "type": null}}, "get_url_for_version_and_platform": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "browser_version", "platform"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "webdriver_manager.drivers.chrome.ChromeDriver.get_url_for_version_and_platform", "name": "get_url_for_version_and_platform", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "webdriver_manager.drivers.chrome.ChromeDriver.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "webdriver_manager.drivers.chrome.ChromeDriver", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ChromeType": {".class": "SymbolTableNode", "cross_ref": "webdriver_manager.core.os_manager.ChromeType", "kind": "Gdef"}, "Driver": {".class": "SymbolTableNode", "cross_ref": "webdriver_manager.core.driver.Driver", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "webdriver_manager.drivers.chrome.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "webdriver_manager.drivers.chrome.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "webdriver_manager.drivers.chrome.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "webdriver_manager.drivers.chrome.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "webdriver_manager.drivers.chrome.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "webdriver_manager.drivers.chrome.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "json": {".class": "SymbolTableNode", "cross_ref": "json", "kind": "Gdef"}, "log": {".class": "SymbolTableNode", "cross_ref": "webdriver_manager.core.logger.log", "kind": "Gdef"}, "version": {".class": "SymbolTableNode", "cross_ref": "packaging.version", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\webdriver_manager\\drivers\\chrome.py"}