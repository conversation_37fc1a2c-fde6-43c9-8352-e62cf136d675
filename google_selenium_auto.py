from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.chrome import ChromeDriverManager
import time


def setup_chrome_driver():
    """
    Konfiguruje i zwraca driver Chrome dla Selenium
    z automatycznym zarządzaniem ChromeDriver
    """
    # Opcje Chrome
    chrome_options = Options()

    # Opcjonalne ustawienia:
    # chrome_options.add_argument("--headless")  # Uruchom bez GUI
    chrome_options.add_argument(
        "--disable-blink-features=AutomationControlled")
    chrome_options.add_experimental_option(
        "excludeSwitches",
        ["enable-automation"])
    chrome_options.add_experimental_option('useAutomationExtension', False)

    # Automatyczne pobieranie i zarządzanie ChromeDriver
    service = Service(ChromeDriverManager().install())
    driver = webdriver.Chrome(service=service, options=chrome_options)

    # Usuń flagę webdriver
    driver.execute_script(
        "Object.defineProperty(navigator,'webdriver', {get: () => undefined})")

    return driver


def go_to_google():
    """
    Główna funkcja - otwiera Google w przeglądarce Chrome
    """
    driver = None

    try:
        print("Uruchamianie przeglądarki Chrome...")
        driver = setup_chrome_driver()

        print("Przechodzenie na stronę Google...")
        driver.get("https://www.google.com")

        # Czekaj aż strona się załaduje
        wait = WebDriverWait(driver, 10)
        #search_box = wait.until(EC.presence_of_element_located((By.NAME, "q")))

        print("Strona Google została załadowana pomyślnie!")
        print(f"Tytuł strony: {driver.title}")
        print(f"URL: {driver.current_url}")

        # Opcjonalnie: możesz dodać interakcję z polem wyszukiwania
        # search_box.send_keys("Selenium Python")
        # search_box.submit()

        # Poczekaj 5 sekund żeby zobaczyć stronę
        print("Czekam 5 sekund...")
        time.sleep(5)

    except Exception as e:
        print(f"Wystąpił błąd: {e}")

    finally:
        if driver:
            print("Zamykanie przeglądarki...")
            driver.quit()


if __name__ == "__main__":
    go_to_google()
