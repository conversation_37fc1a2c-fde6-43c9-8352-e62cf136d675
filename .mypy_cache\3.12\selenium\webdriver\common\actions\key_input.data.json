{".class": "MypyFile", "_fullname": "selenium.webdriver.common.actions.key_input", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "InputDevice": {".class": "SymbolTableNode", "cross_ref": "selenium.webdriver.common.actions.input_device.InputDevice", "kind": "Gdef"}, "Interaction": {".class": "SymbolTableNode", "cross_ref": "selenium.webdriver.common.actions.interaction.Interaction", "kind": "Gdef"}, "KeyInput": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["selenium.webdriver.common.actions.input_device.InputDevice"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "selenium.webdriver.common.actions.key_input.KeyInput", "name": "KeyInput", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "selenium.webdriver.common.actions.key_input.KeyInput", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "selenium.webdriver.common.actions.key_input", "mro": ["selenium.webdriver.common.actions.key_input.KeyInput", "selenium.webdriver.common.actions.input_device.InputDevice", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "selenium.webdriver.common.actions.key_input.KeyInput.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "name"], "arg_types": ["selenium.webdriver.common.actions.key_input.KeyInput", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of KeyInput", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_key_down": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "key"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "selenium.webdriver.common.actions.key_input.KeyInput.create_key_down", "name": "create_key_down", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "key"], "arg_types": ["selenium.webdriver.common.actions.key_input.KeyInput", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_key_down of KeyInput", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_key_up": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "key"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "selenium.webdriver.common.actions.key_input.KeyInput.create_key_up", "name": "create_key_up", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "key"], "arg_types": ["selenium.webdriver.common.actions.key_input.KeyInput", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_key_up of KeyInput", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_pause": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "pause_duration"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "selenium.webdriver.common.actions.key_input.KeyInput.create_pause", "name": "create_pause", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "pause_duration"], "arg_types": ["selenium.webdriver.common.actions.key_input.KeyInput", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_pause of KeyInput", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "encode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "selenium.webdriver.common.actions.key_input.KeyInput.encode", "name": "encode", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["selenium.webdriver.common.actions.key_input.KeyInput"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "encode of KeyInput", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "type": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "selenium.webdriver.common.actions.key_input.KeyInput.type", "name": "type", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "selenium.webdriver.common.actions.key_input.KeyInput.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "selenium.webdriver.common.actions.key_input.KeyInput", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Pause": {".class": "SymbolTableNode", "cross_ref": "selenium.webdriver.common.actions.interaction.Pause", "kind": "Gdef"}, "TypingInteraction": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["selenium.webdriver.common.actions.interaction.Interaction"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "selenium.webdriver.common.actions.key_input.TypingInteraction", "name": "TypingInteraction", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "selenium.webdriver.common.actions.key_input.TypingInteraction", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "selenium.webdriver.common.actions.key_input", "mro": ["selenium.webdriver.common.actions.key_input.TypingInteraction", "selenium.webdriver.common.actions.interaction.Interaction", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "source", "type_", "key"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "selenium.webdriver.common.actions.key_input.TypingInteraction.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "source", "type_", "key"], "arg_types": ["selenium.webdriver.common.actions.key_input.TypingInteraction", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TypingInteraction", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "encode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "selenium.webdriver.common.actions.key_input.TypingInteraction.encode", "name": "encode", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["selenium.webdriver.common.actions.key_input.TypingInteraction"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "encode of TypingInteraction", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "key": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "selenium.webdriver.common.actions.key_input.TypingInteraction.key", "name": "key", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "type": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "selenium.webdriver.common.actions.key_input.TypingInteraction.type", "name": "type", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "selenium.webdriver.common.actions.key_input.TypingInteraction.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "selenium.webdriver.common.actions.key_input.TypingInteraction", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "selenium.webdriver.common.actions.key_input.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "selenium.webdriver.common.actions.key_input.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "selenium.webdriver.common.actions.key_input.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "selenium.webdriver.common.actions.key_input.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "selenium.webdriver.common.actions.key_input.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "selenium.webdriver.common.actions.key_input.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "interaction": {".class": "SymbolTableNode", "cross_ref": "selenium.webdriver.common.actions.interaction", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\selenium\\webdriver\\common\\actions\\key_input.py"}