{".class": "MypyFile", "_fullname": "selenium.webdriver.safari.webdriver", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "DriverFinder": {".class": "SymbolTableNode", "cross_ref": "selenium.webdriver.common.driver_finder.DriverFinder", "kind": "Gdef"}, "Options": {".class": "SymbolTableNode", "cross_ref": "selenium.webdriver.safari.options.Options", "kind": "Gdef"}, "RemoteWebDriver": {".class": "SymbolTableNode", "cross_ref": "selenium.webdriver.remote.webdriver.WebDriver", "kind": "Gdef"}, "SafariRemoteConnection": {".class": "SymbolTableNode", "cross_ref": "selenium.webdriver.safari.remote_connection.SafariRemoteConnection", "kind": "Gdef"}, "Service": {".class": "SymbolTableNode", "cross_ref": "selenium.webdriver.safari.service.Service", "kind": "Gdef"}, "WebDriver": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["selenium.webdriver.remote.webdriver.WebDriver"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "selenium.webdriver.safari.webdriver.WebDriver", "name": "WebDriver", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "selenium.webdriver.safari.webdriver.WebDriver", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "selenium.webdriver.safari.webdriver", "mro": ["selenium.webdriver.safari.webdriver.WebDriver", "selenium.webdriver.remote.webdriver.WebDriver", "selenium.webdriver.remote.webdriver.BaseWebDriver", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["self", "reuse_service", "keep_alive", "options", "service"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "selenium.webdriver.safari.webdriver.WebDriver.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["self", "reuse_service", "keep_alive", "options", "service"], "arg_types": ["selenium.webdriver.safari.webdriver.WebDriver", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "selenium.webdriver.safari.options.Options", "selenium.webdriver.safari.service.Service"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of WebDriver", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_reuse_service": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "selenium.webdriver.safari.webdriver.WebDriver._reuse_service", "name": "_reuse_service", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "builtins.bool"], "uses_pep604_syntax": false}}}, "debug": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "selenium.webdriver.safari.webdriver.WebDriver.debug", "name": "debug", "type": null}}, "get_permission": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "permission"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "selenium.webdriver.safari.webdriver.WebDriver.get_permission", "name": "get_permission", "type": null}}, "quit": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "selenium.webdriver.safari.webdriver.WebDriver.quit", "name": "quit", "type": null}}, "service": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "selenium.webdriver.safari.webdriver.WebDriver.service", "name": "service", "type": "selenium.webdriver.safari.service.Service"}}, "set_permission": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "permission", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "selenium.webdriver.safari.webdriver.WebDriver.set_permission", "name": "set_permission", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "selenium.webdriver.safari.webdriver.WebDriver.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "selenium.webdriver.safari.webdriver.WebDriver", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "WebDriverException": {".class": "SymbolTableNode", "cross_ref": "selenium.common.exceptions.WebDriverException", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "selenium.webdriver.safari.webdriver.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "selenium.webdriver.safari.webdriver.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "selenium.webdriver.safari.webdriver.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "selenium.webdriver.safari.webdriver.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "selenium.webdriver.safari.webdriver.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "selenium.webdriver.safari.webdriver.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "http_client": {".class": "SymbolTableNode", "cross_ref": "http.client", "kind": "Gdef"}, "warnings": {".class": "SymbolTableNode", "cross_ref": "warnings", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\selenium\\webdriver\\safari\\webdriver.py"}