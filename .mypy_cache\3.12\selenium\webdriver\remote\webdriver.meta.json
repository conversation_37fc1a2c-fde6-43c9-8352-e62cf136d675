{"data_mtime": 1757882372, "dep_lines": [44, 43, 45, 46, 47, 48, 53, 55, 56, 57, 58, 60, 61, 62, 63, 64, 65, 38, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 32, 1053, 1055, 1072, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 10, 10, 10, 5, 10, 10, 5, 5, 20, 20, 20, 5, 30, 30, 30, 30], "dependencies": ["selenium.webdriver.common.html5.application_cache", "selenium.webdriver.common.by", "selenium.webdriver.common.options", "selenium.webdriver.common.print_page_options", "selenium.webdriver.common.timeouts", "selenium.webdriver.common.virtual_authenticator", "selenium.webdriver.support.relative_locator", "selenium.webdriver.remote.bidi_connection", "selenium.webdriver.remote.command", "selenium.webdriver.remote.errorhandler", "selenium.webdriver.remote.file_detector", "selenium.webdriver.remote.mobile", "selenium.webdriver.remote.remote_connection", "selenium.webdriver.remote.script_key", "selenium.webdriver.remote.shadowroot", "selenium.webdriver.remote.switch_to", "selenium.webdriver.remote.webelement", "selenium.common.exceptions", "base64", "contextlib", "copy", "os", "pkgu<PERSON>", "types", "typing", "warnings", "zipfile", "abc", "importlib", "json", "urllib3", "re", "builtins", "_collections_abc", "_frozen_importlib", "selenium.common", "selenium.webdriver.common"], "hash": "c984972f13121baca939b99a714994d30b54eacf", "id": "selenium.webdriver.remote.webdriver", "ignore_all": true, "interface_hash": "001daa0ccb78d2004b831972bbd3ea0a9d6e73f6", "mtime": 1757882171, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\selenium\\webdriver\\remote\\webdriver.py", "plugin_data": null, "size": 40837, "suppressed": [], "version_id": "1.15.0"}