{".class": "MypyFile", "_fullname": "selenium.webdriver.remote.shadowroot", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "By": {".class": "SymbolTableNode", "cross_ref": "selenium.webdriver.common.by.By", "kind": "Gdef"}, "Command": {".class": "SymbolTableNode", "cross_ref": "selenium.webdriver.remote.command.Command", "kind": "Gdef"}, "ShadowRoot": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "selenium.webdriver.remote.shadowroot.ShadowRoot", "name": "ShadowRoot", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "selenium.webdriver.remote.shadowroot.ShadowRoot", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "selenium.webdriver.remote.shadowroot", "mro": ["selenium.webdriver.remote.shadowroot.ShadowRoot", "builtins.object"], "names": {".class": "SymbolTable", "__eq__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "selenium.webdriver.remote.shadowroot.ShadowRoot.__eq__", "name": "__eq__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["selenium.webdriver.remote.shadowroot.ShadowRoot", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__eq__ of ShadowRoot", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "selenium.webdriver.remote.shadowroot.ShadowRoot.__hash__", "name": "__hash__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["selenium.webdriver.remote.shadowroot.ShadowRoot"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__hash__ of ShadowRoot", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "session", "id_"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "selenium.webdriver.remote.shadowroot.ShadowRoot.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "session", "id_"], "arg_types": ["selenium.webdriver.remote.shadowroot.ShadowRoot", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ShadowRoot", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "selenium.webdriver.remote.shadowroot.ShadowRoot.__repr__", "name": "__repr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["selenium.webdriver.remote.shadowroot.ShadowRoot"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__repr__ of ShadowRoot", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_execute": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "command", "params"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "selenium.webdriver.remote.shadowroot.ShadowRoot._execute", "name": "_execute", "type": null}}, "_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "selenium.webdriver.remote.shadowroot.ShadowRoot._id", "name": "_id", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "find_element": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "by", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "selenium.webdriver.remote.shadowroot.ShadowRoot.find_element", "name": "find_element", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "by", "value"], "arg_types": ["selenium.webdriver.remote.shadowroot.ShadowRoot", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "find_element of ShadowRoot", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "find_elements": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "by", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "selenium.webdriver.remote.shadowroot.ShadowRoot.find_elements", "name": "find_elements", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "by", "value"], "arg_types": ["selenium.webdriver.remote.shadowroot.ShadowRoot", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "find_elements of ShadowRoot", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "session": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "selenium.webdriver.remote.shadowroot.ShadowRoot.session", "name": "session", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "selenium.webdriver.remote.shadowroot.ShadowRoot.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "selenium.webdriver.remote.shadowroot.ShadowRoot", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "selenium.webdriver.remote.shadowroot.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "selenium.webdriver.remote.shadowroot.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "selenium.webdriver.remote.shadowroot.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "selenium.webdriver.remote.shadowroot.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "selenium.webdriver.remote.shadowroot.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "selenium.webdriver.remote.shadowroot.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "md5_hash": {".class": "SymbolTableNode", "cross_ref": "_hashlib.openssl_md5", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\selenium\\webdriver\\remote\\shadowroot.py"}