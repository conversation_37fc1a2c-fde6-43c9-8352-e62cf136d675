{"data_mtime": 1757882370, "dep_lines": [22, 23, 20, 19, 17, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 10, 5, 30, 30, 30, 30], "dependencies": ["selenium.webdriver.common.actions.input_device", "selenium.webdriver.common.actions.interaction", "selenium.webdriver.remote.webelement", "selenium.common.exceptions", "typing", "builtins", "_frozen_importlib", "abc", "selenium.common", "selenium.webdriver.remote"], "hash": "7f4cb996fcc34935b9e67d401d8d99ba1ca23e17", "id": "selenium.webdriver.common.actions.pointer_input", "ignore_all": true, "interface_hash": "a709174d93e7c51029cbc48dce1f8dd9d525d41a", "mtime": 1757882171, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\selenium\\webdriver\\common\\actions\\pointer_input.py", "plugin_data": null, "size": 2982, "suppressed": [], "version_id": "1.15.0"}