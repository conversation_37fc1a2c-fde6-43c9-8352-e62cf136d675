{".class": "MypyFile", "_fullname": "selenium.common.exceptions", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ERROR_URL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "selenium.common.exceptions.ERROR_URL", "name": "ERROR_URL", "type": "builtins.str"}}, "ElementClickInterceptedException": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["selenium.common.exceptions.WebDriverException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "selenium.common.exceptions.ElementClickInterceptedException", "name": "ElementClickInterceptedException", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "selenium.common.exceptions.ElementClickInterceptedException", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "selenium.common.exceptions", "mro": ["selenium.common.exceptions.ElementClickInterceptedException", "selenium.common.exceptions.WebDriverException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "selenium.common.exceptions.ElementClickInterceptedException.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "selenium.common.exceptions.ElementClickInterceptedException", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ElementNotInteractableException": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["selenium.common.exceptions.InvalidElementStateException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "selenium.common.exceptions.ElementNotInteractableException", "name": "ElementNotInteractableException", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "selenium.common.exceptions.ElementNotInteractableException", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "selenium.common.exceptions", "mro": ["selenium.common.exceptions.ElementNotInteractableException", "selenium.common.exceptions.InvalidElementStateException", "selenium.common.exceptions.WebDriverException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "selenium.common.exceptions.ElementNotInteractableException.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "selenium.common.exceptions.ElementNotInteractableException", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ElementNotSelectableException": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["selenium.common.exceptions.InvalidElementStateException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "selenium.common.exceptions.ElementNotSelectableException", "name": "ElementNotSelectableException", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "selenium.common.exceptions.ElementNotSelectableException", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "selenium.common.exceptions", "mro": ["selenium.common.exceptions.ElementNotSelectableException", "selenium.common.exceptions.InvalidElementStateException", "selenium.common.exceptions.WebDriverException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "selenium.common.exceptions.ElementNotSelectableException.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "selenium.common.exceptions.ElementNotSelectableException", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ElementNotVisibleException": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["selenium.common.exceptions.InvalidElementStateException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "selenium.common.exceptions.ElementNotVisibleException", "name": "ElementNotVisibleException", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "selenium.common.exceptions.ElementNotVisibleException", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "selenium.common.exceptions", "mro": ["selenium.common.exceptions.ElementNotVisibleException", "selenium.common.exceptions.InvalidElementStateException", "selenium.common.exceptions.WebDriverException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "selenium.common.exceptions.ElementNotVisibleException.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "selenium.common.exceptions.ElementNotVisibleException", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ImeActivationFailedException": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["selenium.common.exceptions.WebDriverException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "selenium.common.exceptions.ImeActivationFailedException", "name": "ImeActivationFailedException", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "selenium.common.exceptions.ImeActivationFailedException", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "selenium.common.exceptions", "mro": ["selenium.common.exceptions.ImeActivationFailedException", "selenium.common.exceptions.WebDriverException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "selenium.common.exceptions.ImeActivationFailedException.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "selenium.common.exceptions.ImeActivationFailedException", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ImeNotAvailableException": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["selenium.common.exceptions.WebDriverException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "selenium.common.exceptions.ImeNotAvailableException", "name": "ImeNotAvailableException", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "selenium.common.exceptions.ImeNotAvailableException", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "selenium.common.exceptions", "mro": ["selenium.common.exceptions.ImeNotAvailableException", "selenium.common.exceptions.WebDriverException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "selenium.common.exceptions.ImeNotAvailableException.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "selenium.common.exceptions.ImeNotAvailableException", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InsecureCertificateException": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["selenium.common.exceptions.WebDriverException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "selenium.common.exceptions.InsecureCertificateException", "name": "InsecureCertificateException", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "selenium.common.exceptions.InsecureCertificateException", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "selenium.common.exceptions", "mro": ["selenium.common.exceptions.InsecureCertificateException", "selenium.common.exceptions.WebDriverException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "selenium.common.exceptions.InsecureCertificateException.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "selenium.common.exceptions.InsecureCertificateException", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InvalidArgumentException": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["selenium.common.exceptions.WebDriverException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "selenium.common.exceptions.InvalidArgumentException", "name": "InvalidArgumentException", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "selenium.common.exceptions.InvalidArgumentException", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "selenium.common.exceptions", "mro": ["selenium.common.exceptions.InvalidArgumentException", "selenium.common.exceptions.WebDriverException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "selenium.common.exceptions.InvalidArgumentException.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "selenium.common.exceptions.InvalidArgumentException", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InvalidCookieDomainException": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["selenium.common.exceptions.WebDriverException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "selenium.common.exceptions.InvalidCookieDomainException", "name": "InvalidCookieDomainException", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "selenium.common.exceptions.InvalidCookieDomainException", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "selenium.common.exceptions", "mro": ["selenium.common.exceptions.InvalidCookieDomainException", "selenium.common.exceptions.WebDriverException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "selenium.common.exceptions.InvalidCookieDomainException.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "selenium.common.exceptions.InvalidCookieDomainException", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InvalidCoordinatesException": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["selenium.common.exceptions.WebDriverException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "selenium.common.exceptions.InvalidCoordinatesException", "name": "InvalidCoordinatesException", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "selenium.common.exceptions.InvalidCoordinatesException", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "selenium.common.exceptions", "mro": ["selenium.common.exceptions.InvalidCoordinatesException", "selenium.common.exceptions.WebDriverException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "selenium.common.exceptions.InvalidCoordinatesException.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "selenium.common.exceptions.InvalidCoordinatesException", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InvalidElementStateException": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["selenium.common.exceptions.WebDriverException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "selenium.common.exceptions.InvalidElementStateException", "name": "InvalidElementStateException", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "selenium.common.exceptions.InvalidElementStateException", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "selenium.common.exceptions", "mro": ["selenium.common.exceptions.InvalidElementStateException", "selenium.common.exceptions.WebDriverException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "selenium.common.exceptions.InvalidElementStateException.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "selenium.common.exceptions.InvalidElementStateException", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InvalidSelectorException": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["selenium.common.exceptions.WebDriverException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "selenium.common.exceptions.InvalidSelectorException", "name": "InvalidSelectorException", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "selenium.common.exceptions.InvalidSelectorException", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "selenium.common.exceptions", "mro": ["selenium.common.exceptions.InvalidSelectorException", "selenium.common.exceptions.WebDriverException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "msg", "screen", "stacktrace"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "selenium.common.exceptions.InvalidSelectorException.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "msg", "screen", "stacktrace"], "arg_types": ["selenium.common.exceptions.InvalidSelectorException", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of InvalidSelectorException", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "selenium.common.exceptions.InvalidSelectorException.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "selenium.common.exceptions.InvalidSelectorException", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InvalidSessionIdException": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["selenium.common.exceptions.WebDriverException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "selenium.common.exceptions.InvalidSessionIdException", "name": "InvalidSessionIdException", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "selenium.common.exceptions.InvalidSessionIdException", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "selenium.common.exceptions", "mro": ["selenium.common.exceptions.InvalidSessionIdException", "selenium.common.exceptions.WebDriverException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "selenium.common.exceptions.InvalidSessionIdException.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "selenium.common.exceptions.InvalidSessionIdException", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InvalidSwitchToTargetException": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["selenium.common.exceptions.WebDriverException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "selenium.common.exceptions.InvalidSwitchToTargetException", "name": "InvalidSwitchToTargetException", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "selenium.common.exceptions.InvalidSwitchToTargetException", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "selenium.common.exceptions", "mro": ["selenium.common.exceptions.InvalidSwitchToTargetException", "selenium.common.exceptions.WebDriverException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "selenium.common.exceptions.InvalidSwitchToTargetException.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "selenium.common.exceptions.InvalidSwitchToTargetException", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "JavascriptException": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["selenium.common.exceptions.WebDriverException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "selenium.common.exceptions.JavascriptException", "name": "JavascriptException", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "selenium.common.exceptions.JavascriptException", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "selenium.common.exceptions", "mro": ["selenium.common.exceptions.JavascriptException", "selenium.common.exceptions.WebDriverException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "selenium.common.exceptions.JavascriptException.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "selenium.common.exceptions.JavascriptException", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MoveTargetOutOfBoundsException": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["selenium.common.exceptions.WebDriverException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "selenium.common.exceptions.MoveTargetOutOfBoundsException", "name": "MoveTargetOutOfBoundsException", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "selenium.common.exceptions.MoveTargetOutOfBoundsException", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "selenium.common.exceptions", "mro": ["selenium.common.exceptions.MoveTargetOutOfBoundsException", "selenium.common.exceptions.WebDriverException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "selenium.common.exceptions.MoveTargetOutOfBoundsException.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "selenium.common.exceptions.MoveTargetOutOfBoundsException", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NoAlertPresentException": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["selenium.common.exceptions.WebDriverException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "selenium.common.exceptions.NoAlertPresentException", "name": "NoAlertPresentException", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "selenium.common.exceptions.NoAlertPresentException", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "selenium.common.exceptions", "mro": ["selenium.common.exceptions.NoAlertPresentException", "selenium.common.exceptions.WebDriverException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "selenium.common.exceptions.NoAlertPresentException.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "selenium.common.exceptions.NoAlertPresentException", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NoSuchAttributeException": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["selenium.common.exceptions.WebDriverException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "selenium.common.exceptions.NoSuchAttributeException", "name": "NoSuchAttributeException", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "selenium.common.exceptions.NoSuchAttributeException", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "selenium.common.exceptions", "mro": ["selenium.common.exceptions.NoSuchAttributeException", "selenium.common.exceptions.WebDriverException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "selenium.common.exceptions.NoSuchAttributeException.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "selenium.common.exceptions.NoSuchAttributeException", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NoSuchCookieException": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["selenium.common.exceptions.WebDriverException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "selenium.common.exceptions.NoSuchCookieException", "name": "NoSuchCookieException", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "selenium.common.exceptions.NoSuchCookieException", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "selenium.common.exceptions", "mro": ["selenium.common.exceptions.NoSuchCookieException", "selenium.common.exceptions.WebDriverException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "selenium.common.exceptions.NoSuchCookieException.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "selenium.common.exceptions.NoSuchCookieException", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NoSuchDriverException": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["selenium.common.exceptions.WebDriverException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "selenium.common.exceptions.NoSuchDriverException", "name": "NoSuchDriverException", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "selenium.common.exceptions.NoSuchDriverException", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "selenium.common.exceptions", "mro": ["selenium.common.exceptions.NoSuchDriverException", "selenium.common.exceptions.WebDriverException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "msg", "screen", "stacktrace"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "selenium.common.exceptions.NoSuchDriverException.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "msg", "screen", "stacktrace"], "arg_types": ["selenium.common.exceptions.NoSuchDriverException", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of NoSuchDriverException", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "selenium.common.exceptions.NoSuchDriverException.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "selenium.common.exceptions.NoSuchDriverException", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NoSuchElementException": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["selenium.common.exceptions.WebDriverException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "selenium.common.exceptions.NoSuchElementException", "name": "NoSuchElementException", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "selenium.common.exceptions.NoSuchElementException", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "selenium.common.exceptions", "mro": ["selenium.common.exceptions.NoSuchElementException", "selenium.common.exceptions.WebDriverException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "msg", "screen", "stacktrace"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "selenium.common.exceptions.NoSuchElementException.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "msg", "screen", "stacktrace"], "arg_types": ["selenium.common.exceptions.NoSuchElementException", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of NoSuchElementException", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "selenium.common.exceptions.NoSuchElementException.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "selenium.common.exceptions.NoSuchElementException", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NoSuchFrameException": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["selenium.common.exceptions.InvalidSwitchToTargetException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "selenium.common.exceptions.NoSuchFrameException", "name": "NoSuchFrameException", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "selenium.common.exceptions.NoSuchFrameException", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "selenium.common.exceptions", "mro": ["selenium.common.exceptions.NoSuchFrameException", "selenium.common.exceptions.InvalidSwitchToTargetException", "selenium.common.exceptions.WebDriverException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "selenium.common.exceptions.NoSuchFrameException.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "selenium.common.exceptions.NoSuchFrameException", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NoSuchShadowRootException": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["selenium.common.exceptions.WebDriverException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "selenium.common.exceptions.NoSuchShadowRootException", "name": "NoSuchShadowRootException", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "selenium.common.exceptions.NoSuchShadowRootException", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "selenium.common.exceptions", "mro": ["selenium.common.exceptions.NoSuchShadowRootException", "selenium.common.exceptions.WebDriverException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "selenium.common.exceptions.NoSuchShadowRootException.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "selenium.common.exceptions.NoSuchShadowRootException", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NoSuchWindowException": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["selenium.common.exceptions.InvalidSwitchToTargetException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "selenium.common.exceptions.NoSuchWindowException", "name": "NoSuchWindowException", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "selenium.common.exceptions.NoSuchWindowException", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "selenium.common.exceptions", "mro": ["selenium.common.exceptions.NoSuchWindowException", "selenium.common.exceptions.InvalidSwitchToTargetException", "selenium.common.exceptions.WebDriverException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "selenium.common.exceptions.NoSuchWindowException.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "selenium.common.exceptions.NoSuchWindowException", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "SUPPORT_MSG": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "selenium.common.exceptions.SUPPORT_MSG", "name": "SUPPORT_MSG", "type": "builtins.str"}}, "ScreenshotException": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["selenium.common.exceptions.WebDriverException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "selenium.common.exceptions.ScreenshotException", "name": "ScreenshotException", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "selenium.common.exceptions.ScreenshotException", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "selenium.common.exceptions", "mro": ["selenium.common.exceptions.ScreenshotException", "selenium.common.exceptions.WebDriverException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "selenium.common.exceptions.ScreenshotException.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "selenium.common.exceptions.ScreenshotException", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef"}, "SessionNotCreatedException": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["selenium.common.exceptions.WebDriverException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "selenium.common.exceptions.SessionNotCreatedException", "name": "SessionNotCreatedException", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "selenium.common.exceptions.SessionNotCreatedException", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "selenium.common.exceptions", "mro": ["selenium.common.exceptions.SessionNotCreatedException", "selenium.common.exceptions.WebDriverException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "selenium.common.exceptions.SessionNotCreatedException.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "selenium.common.exceptions.SessionNotCreatedException", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "StaleElementReferenceException": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["selenium.common.exceptions.WebDriverException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "selenium.common.exceptions.StaleElementReferenceException", "name": "StaleElementReferenceException", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "selenium.common.exceptions.StaleElementReferenceException", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "selenium.common.exceptions", "mro": ["selenium.common.exceptions.StaleElementReferenceException", "selenium.common.exceptions.WebDriverException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "msg", "screen", "stacktrace"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "selenium.common.exceptions.StaleElementReferenceException.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "msg", "screen", "stacktrace"], "arg_types": ["selenium.common.exceptions.StaleElementReferenceException", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of StaleElementReferenceException", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "selenium.common.exceptions.StaleElementReferenceException.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "selenium.common.exceptions.StaleElementReferenceException", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TimeoutException": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["selenium.common.exceptions.WebDriverException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "selenium.common.exceptions.TimeoutException", "name": "TimeoutException", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "selenium.common.exceptions.TimeoutException", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "selenium.common.exceptions", "mro": ["selenium.common.exceptions.TimeoutException", "selenium.common.exceptions.WebDriverException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "selenium.common.exceptions.TimeoutException.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "selenium.common.exceptions.TimeoutException", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "UnableToSetCookieException": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["selenium.common.exceptions.WebDriverException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "selenium.common.exceptions.UnableToSetCookieException", "name": "UnableToSetCookieException", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "selenium.common.exceptions.UnableToSetCookieException", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "selenium.common.exceptions", "mro": ["selenium.common.exceptions.UnableToSetCookieException", "selenium.common.exceptions.WebDriverException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "selenium.common.exceptions.UnableToSetCookieException.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "selenium.common.exceptions.UnableToSetCookieException", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "UnexpectedAlertPresentException": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["selenium.common.exceptions.WebDriverException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "selenium.common.exceptions.UnexpectedAlertPresentException", "name": "UnexpectedAlertPresentException", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "selenium.common.exceptions.UnexpectedAlertPresentException", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "selenium.common.exceptions", "mro": ["selenium.common.exceptions.UnexpectedAlertPresentException", "selenium.common.exceptions.WebDriverException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["self", "msg", "screen", "stacktrace", "alert_text"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "selenium.common.exceptions.UnexpectedAlertPresentException.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["self", "msg", "screen", "stacktrace", "alert_text"], "arg_types": ["selenium.common.exceptions.UnexpectedAlertPresentException", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of UnexpectedAlertPresentException", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__str__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "selenium.common.exceptions.UnexpectedAlertPresentException.__str__", "name": "__str__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["selenium.common.exceptions.UnexpectedAlertPresentException"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__str__ of UnexpectedAlertPresentException", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "alert_text": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "selenium.common.exceptions.UnexpectedAlertPresentException.alert_text", "name": "alert_text", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "selenium.common.exceptions.UnexpectedAlertPresentException.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "selenium.common.exceptions.UnexpectedAlertPresentException", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "UnexpectedTagNameException": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["selenium.common.exceptions.WebDriverException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "selenium.common.exceptions.UnexpectedTagNameException", "name": "UnexpectedTagNameException", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "selenium.common.exceptions.UnexpectedTagNameException", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "selenium.common.exceptions", "mro": ["selenium.common.exceptions.UnexpectedTagNameException", "selenium.common.exceptions.WebDriverException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "selenium.common.exceptions.UnexpectedTagNameException.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "selenium.common.exceptions.UnexpectedTagNameException", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "UnknownMethodException": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["selenium.common.exceptions.WebDriverException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "selenium.common.exceptions.UnknownMethodException", "name": "UnknownMethodException", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "selenium.common.exceptions.UnknownMethodException", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "selenium.common.exceptions", "mro": ["selenium.common.exceptions.UnknownMethodException", "selenium.common.exceptions.WebDriverException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "selenium.common.exceptions.UnknownMethodException.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "selenium.common.exceptions.UnknownMethodException", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "WebDriverException": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.Exception"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "selenium.common.exceptions.WebDriverException", "name": "WebDriverException", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "selenium.common.exceptions.WebDriverException", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "selenium.common.exceptions", "mro": ["selenium.common.exceptions.WebDriverException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "msg", "screen", "stacktrace"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "selenium.common.exceptions.WebDriverException.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "msg", "screen", "stacktrace"], "arg_types": ["selenium.common.exceptions.WebDriverException", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of WebDriverException", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__str__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "selenium.common.exceptions.WebDriverException.__str__", "name": "__str__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["selenium.common.exceptions.WebDriverException"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__str__ of WebDriverException", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "msg": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "selenium.common.exceptions.WebDriverException.msg", "name": "msg", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "screen": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "selenium.common.exceptions.WebDriverException.screen", "name": "screen", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "stacktrace": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "selenium.common.exceptions.WebDriverException.stacktrace", "name": "stacktrace", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "selenium.common.exceptions.WebDriverException.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "selenium.common.exceptions.WebDriverException", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "selenium.common.exceptions.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "selenium.common.exceptions.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "selenium.common.exceptions.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "selenium.common.exceptions.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "selenium.common.exceptions.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "selenium.common.exceptions.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\selenium\\common\\exceptions.py"}