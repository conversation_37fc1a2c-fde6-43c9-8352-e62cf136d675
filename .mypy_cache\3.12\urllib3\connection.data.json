{".class": "MypyFile", "_fullname": "urllib3.connection", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "BaseSSLError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "urllib3.connection.BaseSSLError", "line": 33, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "ssl.SSLError"}}, "BrokenPipeError": {".class": "SymbolTableNode", "cross_ref": "builtins.BrokenPipeError", "kind": "Gdef"}, "CertificateError": {".class": "SymbolTableNode", "cross_ref": "urllib3.util.ssl_match_hostname.CertificateError", "kind": "Gdef"}, "ConnectTimeoutError": {".class": "SymbolTableNode", "cross_ref": "urllib3.exceptions.ConnectTimeoutError", "kind": "Gdef"}, "ConnectionError": {".class": "SymbolTableNode", "cross_ref": "builtins.ConnectionError", "kind": "Gdef"}, "DummyConnection": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "urllib3.connection.DummyConnection", "name": "DummyConnection", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "urllib3.connection.DummyConnection", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "urllib3.connection", "mro": ["urllib3.connection.DummyConnection", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "urllib3.connection.DummyConnection.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "urllib3.connection.DummyConnection", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HTTPConnection": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["http.client.HTTPConnection"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "urllib3.connection.HTTPConnection", "name": "HTTPConnection", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "urllib3.connection.HTTPConnection", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "urllib3.connection", "mro": ["urllib3.connection.HTTPConnection", "http.client.HTTPConnection", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "host", "port", "timeout", "source_address", "blocksize", "socket_options", "proxy", "proxy_config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "urllib3.connection.HTTPConnection.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "host", "port", "timeout", "source_address", "blocksize", "socket_options", "proxy", "proxy_config"], "arg_types": ["urllib3.connection.HTTPConnection", "builtins.str", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [], "type_ref": "urllib3.util.timeout._TYPE_TIMEOUT"}, {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int", {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "urllib3.util.connection._TYPE_SOCKET_OPTIONS"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "urllib3.util.url.Url"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "urllib3._base_connection.ProxyConfig"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of HTTPConnection", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_dns_host": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "urllib3.connection.HTTPConnection._dns_host", "name": "_dns_host", "type": "builtins.str"}}, "_has_connected_to_proxy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "urllib3.connection.HTTPConnection._has_connected_to_proxy", "name": "_has_connected_to_proxy", "type": "builtins.bool"}}, "_new_conn": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "urllib3.connection.HTTPConnection._new_conn", "name": "_new_conn", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["urllib3.connection.HTTPConnection"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_new_conn of HTTPConnection", "ret_type": "socket.socket", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_raw_proxy_headers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "urllib3.connection.HTTPConnection._raw_proxy_headers", "name": "_raw_proxy_headers", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}, "type_of_any": 7}}}, "_response_options": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "urllib3.connection.HTTPConnection._response_options", "name": "_response_options", "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "urllib3._base_connection._ResponseOptions"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "_tunnel": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "urllib3.connection.HTTPConnection._tunnel", "name": "_tunnel", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["urllib3.connection.HTTPConnection"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_tunnel of HTTPConnection", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_tunnel_host": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "urllib3.connection.HTTPConnection._tunnel_host", "name": "_tunnel_host", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "_tunnel_port": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "urllib3.connection.HTTPConnection._tunnel_port", "name": "_tunnel_port", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "_tunnel_scheme": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "urllib3.connection.HTTPConnection._tunnel_scheme", "name": "_tunnel_scheme", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "_wrap_ipv6": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "ip"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "urllib3.connection.HTTPConnection._wrap_ipv6", "name": "_wrap_ipv6", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "ip"], "arg_types": ["urllib3.connection.HTTPConnection", "builtins.bytes"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_wrap_ipv6 of HTTPConnection", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "blocksize": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "urllib3.connection.HTTPConnection.blocksize", "name": "blocksize", "type": "builtins.int"}}, "close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "urllib3.connection.HTTPConnection.close", "name": "close", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["urllib3.connection.HTTPConnection"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "close of HTTPConnection", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "connect": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "urllib3.connection.HTTPConnection.connect", "name": "connect", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["urllib3.connection.HTTPConnection"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "connect of HTTPConnection", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "default_port": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready", "has_explicit_value"], "fullname": "urllib3.connection.HTTPConnection.default_port", "name": "default_port", "type": "builtins.int"}}, "default_socket_options": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready", "has_explicit_value"], "fullname": "urllib3.connection.HTTPConnection.default_socket_options", "name": "default_socket_options", "type": {".class": "TypeAliasType", "args": [], "type_ref": "urllib3.util.connection._TYPE_SOCKET_OPTIONS"}}}, "getresponse": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "urllib3.connection.HTTPConnection.getresponse", "name": "getresponse", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["urllib3.connection.HTTPConnection"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getresponse of HTTPConnection", "ret_type": "urllib3.response.HTTPResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "has_connected_to_proxy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "urllib3.connection.HTTPConnection.has_connected_to_proxy", "name": "has_connected_to_proxy", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["urllib3.connection.HTTPConnection"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "has_connected_to_proxy of HTTPConnection", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "urllib3.connection.HTTPConnection.has_connected_to_proxy", "name": "has_connected_to_proxy", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["urllib3.connection.HTTPConnection"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "has_connected_to_proxy of HTTPConnection", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "host": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": ["is_property"], "fullname": "urllib3.connection.HTTPConnection.host", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_overload", "is_decorated"], "fullname": "urllib3.connection.HTTPConnection.host", "name": "host", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["urllib3.connection.HTTPConnection"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "host of HTTPConnection", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_settable_property", "is_ready", "is_inferred"], "fullname": "urllib3.connection.HTTPConnection.host", "name": "host", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["urllib3.connection.HTTPConnection"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "host of HTTPConnection", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "urllib3.connection.HTTPConnection.host", "name": "host", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "value"], "arg_types": ["urllib3.connection.HTTPConnection", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "host of HTTPConnection", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_inferred"], "fullname": "", "name": "host", "type": null}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["urllib3.connection.HTTPConnection"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "host of HTTPConnection", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "is_closed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "urllib3.connection.HTTPConnection.is_closed", "name": "is_closed", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["urllib3.connection.HTTPConnection"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_closed of HTTPConnection", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "urllib3.connection.HTTPConnection.is_closed", "name": "is_closed", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["urllib3.connection.HTTPConnection"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_closed of HTTPConnection", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "is_connected": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "urllib3.connection.HTTPConnection.is_connected", "name": "is_connected", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["urllib3.connection.HTTPConnection"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_connected of HTTPConnection", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "urllib3.connection.HTTPConnection.is_connected", "name": "is_connected", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["urllib3.connection.HTTPConnection"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_connected of HTTPConnection", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "is_verified": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "urllib3.connection.HTTPConnection.is_verified", "name": "is_verified", "type": "builtins.bool"}}, "proxy": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "urllib3.connection.HTTPConnection.proxy", "name": "proxy", "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "urllib3.util.url.Url"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "proxy_config": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "urllib3.connection.HTTPConnection.proxy_config", "name": "proxy_config", "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "urllib3._base_connection.ProxyConfig"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "proxy_is_forwarding": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "urllib3.connection.HTTPConnection.proxy_is_forwarding", "name": "proxy_is_forwarding", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["urllib3.connection.HTTPConnection"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "proxy_is_forwarding of HTTPConnection", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "urllib3.connection.HTTPConnection.proxy_is_forwarding", "name": "proxy_is_forwarding", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["urllib3.connection.HTTPConnection"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "proxy_is_forwarding of HTTPConnection", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "proxy_is_tunneling": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "urllib3.connection.HTTPConnection.proxy_is_tunneling", "name": "proxy_is_tunneling", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["urllib3.connection.HTTPConnection"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "proxy_is_tunneling of HTTPConnection", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "urllib3.connection.HTTPConnection.proxy_is_tunneling", "name": "proxy_is_tunneling", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["urllib3.connection.HTTPConnection"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "proxy_is_tunneling of HTTPConnection", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "proxy_is_verified": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "urllib3.connection.HTTPConnection.proxy_is_verified", "name": "proxy_is_verified", "type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "putheader": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2], "arg_names": ["self", "header", "values"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "urllib3.connection.HTTPConnection.putheader", "name": "putheader", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2], "arg_names": ["self", "header", "values"], "arg_types": ["urllib3.connection.HTTPConnection", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "putheader of HTTPConnection", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "putrequest": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "method", "url", "skip_host", "skip_accept_encoding"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "urllib3.connection.HTTPConnection.putrequest", "name": "putrequest", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "method", "url", "skip_host", "skip_accept_encoding"], "arg_types": ["urllib3.connection.HTTPConnection", "builtins.str", "builtins.str", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "putrequest of HTTPConnection", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "request": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 5, 5, 5, 5], "arg_names": ["self", "method", "url", "body", "headers", "chunked", "preload_content", "decode_content", "enforce_content_length"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "urllib3.connection.HTTPConnection.request", "name": "request", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 5, 5, 5, 5], "arg_names": ["self", "method", "url", "body", "headers", "chunked", "preload_content", "decode_content", "enforce_content_length"], "arg_types": ["urllib3.connection.HTTPConnection", "builtins.str", "builtins.str", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "urllib3._base_connection._TYPE_BODY"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "request of HTTPConnection", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "request_chunked": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "method", "url", "body", "headers"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "urllib3.connection.HTTPConnection.request_chunked", "name": "request_chunked", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "method", "url", "body", "headers"], "arg_types": ["urllib3.connection.HTTPConnection", "builtins.str", "builtins.str", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "urllib3._base_connection._TYPE_BODY"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "request_chunked of HTTPConnection", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_tunnel": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "host", "port", "headers", "scheme"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "urllib3.connection.HTTPConnection.set_tunnel", "name": "set_tunnel", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "host", "port", "headers", "scheme"], "arg_types": ["urllib3.connection.HTTPConnection", "builtins.str", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_tunnel of HTTPConnection", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "socket_options": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "urllib3.connection.HTTPConnection.socket_options", "name": "socket_options", "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "urllib3.util.connection._TYPE_SOCKET_OPTIONS"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "source_address": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "urllib3.connection.HTTPConnection.source_address", "name": "source_address", "type": {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "urllib3.connection.HTTPConnection.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "urllib3.connection.HTTPConnection", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HTTPException": {".class": "SymbolTableNode", "cross_ref": "http.client.HTTPException", "kind": "Gdef"}, "HTTPHeaderDict": {".class": "SymbolTableNode", "cross_ref": "urllib3._collections.HTTPHeaderDict", "kind": "Gdef"}, "HTTPResponse": {".class": "SymbolTableNode", "cross_ref": "urllib3.response.HTTPResponse", "kind": "Gdef"}, "HTTPSConnection": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["urllib3.connection.HTTPConnection"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "urllib3.connection.HTTPSConnection", "name": "HTTPSConnection", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "urllib3.connection.HTTPSConnection", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "urllib3.connection", "mro": ["urllib3.connection.HTTPSConnection", "urllib3.connection.HTTPConnection", "http.client.HTTPConnection", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "host", "port", "timeout", "source_address", "blocksize", "socket_options", "proxy", "proxy_config", "cert_reqs", "assert_hostname", "assert_fingerprint", "server_hostname", "ssl_context", "ca_certs", "ca_cert_dir", "ca_cert_data", "ssl_minimum_version", "ssl_maximum_version", "ssl_version", "cert_file", "key_file", "key_password"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "urllib3.connection.HTTPSConnection.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "host", "port", "timeout", "source_address", "blocksize", "socket_options", "proxy", "proxy_config", "cert_reqs", "assert_hostname", "assert_fingerprint", "server_hostname", "ssl_context", "ca_certs", "ca_cert_dir", "ca_cert_data", "ssl_minimum_version", "ssl_maximum_version", "ssl_version", "cert_file", "key_file", "key_password"], "arg_types": ["urllib3.connection.HTTPSConnection", "builtins.str", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [], "type_ref": "urllib3.util.timeout._TYPE_TIMEOUT"}, {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.int", {".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "urllib3.util.connection._TYPE_SOCKET_OPTIONS"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "urllib3.util.url.Url"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "urllib3._base_connection.ProxyConfig"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str", {".class": "LiteralType", "fallback": "builtins.bool", "value": false}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["ssl.SSLContext", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of HTTPSConnection", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_connect_callback": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "urllib3.connection.HTTPSConnection._connect_callback", "name": "_connect_callback", "type": {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "_connect_tls_proxy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "hostname", "sock"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "urllib3.connection.HTTPSConnection._connect_tls_proxy", "name": "_connect_tls_proxy", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "hostname", "sock"], "arg_types": ["urllib3.connection.HTTPSConnection", "builtins.str", "socket.socket"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_connect_tls_proxy of HTTPSConnection", "ret_type": "ssl.SSLSocket", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "assert_fingerprint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "urllib3.connection.HTTPSConnection.assert_fingerprint", "name": "assert_fingerprint", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "assert_hostname": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "urllib3.connection.HTTPSConnection.assert_hostname", "name": "assert_hostname", "type": {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str", {".class": "LiteralType", "fallback": "builtins.bool", "value": false}], "uses_pep604_syntax": true}}}, "ca_cert_data": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "urllib3.connection.HTTPSConnection.ca_cert_data", "name": "ca_cert_data", "type": {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}}}, "ca_cert_dir": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "urllib3.connection.HTTPSConnection.ca_cert_dir", "name": "ca_cert_dir", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "ca_certs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "urllib3.connection.HTTPSConnection.ca_certs", "name": "ca_certs", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "cert_file": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "urllib3.connection.HTTPSConnection.cert_file", "name": "cert_file", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "cert_reqs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "urllib3.connection.HTTPSConnection.cert_reqs", "name": "cert_reqs", "type": {".class": "UnionType", "items": ["builtins.int", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "connect": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "urllib3.connection.HTTPSConnection.connect", "name": "connect", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["urllib3.connection.HTTPSConnection"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "connect of HTTPSConnection", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "default_port": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_inferred", "has_explicit_value"], "fullname": "urllib3.connection.HTTPSConnection.default_port", "name": "default_port", "type": "builtins.int"}}, "key_file": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "urllib3.connection.HTTPSConnection.key_file", "name": "key_file", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "key_password": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "urllib3.connection.HTTPSConnection.key_password", "name": "key_password", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "server_hostname": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "urllib3.connection.HTTPSConnection.server_hostname", "name": "server_hostname", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "set_cert": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "key_file", "cert_file", "cert_reqs", "key_password", "ca_certs", "assert_hostname", "assert_fingerprint", "ca_cert_dir", "ca_cert_data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "urllib3.connection.HTTPSConnection.set_cert", "name": "set_cert", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "key_file", "cert_file", "cert_reqs", "key_password", "ca_certs", "assert_hostname", "assert_fingerprint", "ca_cert_dir", "ca_cert_data"], "arg_types": ["urllib3.connection.HTTPSConnection", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str", {".class": "LiteralType", "fallback": "builtins.bool", "value": false}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_cert of HTTPSConnection", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ssl_context": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "urllib3.connection.HTTPSConnection.ssl_context", "name": "ssl_context", "type": {".class": "UnionType", "items": ["ssl.SSLContext", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "ssl_maximum_version": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "urllib3.connection.HTTPSConnection.ssl_maximum_version", "name": "ssl_maximum_version", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "ssl_minimum_version": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "urllib3.connection.HTTPSConnection.ssl_minimum_version", "name": "ssl_minimum_version", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "ssl_version": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "urllib3.connection.HTTPSConnection.ssl_version", "name": "ssl_version", "type": {".class": "UnionType", "items": ["builtins.int", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "urllib3.connection.HTTPSConnection.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "urllib3.connection.HTTPSConnection", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HeaderParsingError": {".class": "SymbolTableNode", "cross_ref": "urllib3.exceptions.HeaderParsingError", "kind": "Gdef"}, "NameResolutionError": {".class": "SymbolTableNode", "cross_ref": "urllib3.exceptions.NameResolutionError", "kind": "Gdef"}, "NewConnectionError": {".class": "SymbolTableNode", "cross_ref": "urllib3.exceptions.NewConnectionError", "kind": "Gdef"}, "ProxyConfig": {".class": "SymbolTableNode", "cross_ref": "urllib3._base_connection.ProxyConfig", "kind": "Gdef"}, "ProxyError": {".class": "SymbolTableNode", "cross_ref": "urllib3.exceptions.ProxyError", "kind": "Gdef"}, "RECENT_DATE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "urllib3.connection.RECENT_DATE", "name": "RECENT_DATE", "type": "datetime.date"}}, "ResponseNotReady": {".class": "SymbolTableNode", "cross_ref": "http.client.ResponseNotReady", "kind": "Gdef"}, "SKIPPABLE_HEADERS": {".class": "SymbolTableNode", "cross_ref": "urllib3.util.request.SKIPPABLE_HEADERS", "kind": "Gdef"}, "SKIP_HEADER": {".class": "SymbolTableNode", "cross_ref": "urllib3.util.request.SKIP_HEADER", "kind": "Gdef"}, "SSLTransport": {".class": "SymbolTableNode", "cross_ref": "urllib3.util.ssltransport.SSLTransport", "kind": "Gdef"}, "SocketTimeout": {".class": "SymbolTableNode", "cross_ref": "socket.timeout", "kind": "Gdef"}, "SystemTimeWarning": {".class": "SymbolTableNode", "cross_ref": "urllib3.exceptions.SystemTimeWarning", "kind": "Gdef"}, "Timeout": {".class": "SymbolTableNode", "cross_ref": "urllib3.util.timeout.Timeout", "kind": "Gdef"}, "Url": {".class": "SymbolTableNode", "cross_ref": "urllib3.util.url.Url", "kind": "Gdef"}, "VerifiedHTTPSConnection": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "urllib3.connection.VerifiedHTTPSConnection", "line": 1083, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "urllib3.connection.HTTPSConnection"}}, "_CONTAINS_CONTROL_CHAR_RE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "urllib3.connection._CONTAINS_CONTROL_CHAR_RE", "name": "_CONTAINS_CONTROL_CHAR_RE", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "_DEFAULT_TIMEOUT": {".class": "SymbolTableNode", "cross_ref": "urllib3.util.timeout._DEFAULT_TIMEOUT", "kind": "Gdef"}, "_HTTPConnection": {".class": "SymbolTableNode", "cross_ref": "http.client.HTTPConnection", "kind": "Gdef"}, "_ResponseOptions": {".class": "SymbolTableNode", "cross_ref": "urllib3._base_connection._ResponseOptions", "kind": "Gdef"}, "_TYPE_BODY": {".class": "SymbolTableNode", "cross_ref": "urllib3._base_connection._TYPE_BODY", "kind": "Gdef"}, "_TYPE_PEER_CERT_RET_DICT": {".class": "SymbolTableNode", "cross_ref": "urllib3.util.ssl_._TYPE_PEER_CERT_RET_DICT", "kind": "Gdef"}, "_TYPE_TIMEOUT": {".class": "SymbolTableNode", "cross_ref": "urllib3.util.timeout._TYPE_TIMEOUT", "kind": "Gdef"}, "_WrappedAndVerifiedSocket": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["ssl.SSLSocket", "urllib3.util.ssltransport.SSLTransport", "builtins.bool"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "urllib3.connection._WrappedAndVerifiedSocket", "name": "_WrappedAndVerifiedSocket", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_named_tuple"], "fullname": "urllib3.connection._WrappedAndVerifiedSocket", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"namedtuple": {"fields": ["socket", "is_verified"]}}, "module_name": "urllib3.connection", "mro": ["urllib3.connection._WrappedAndVerifiedSocket", "builtins.tuple", "typing.Sequence", "typing.Reversible", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "_NT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "urllib3.connection._WrappedAndVerifiedSocket._NT", "name": "_NT", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": ["ssl.SSLSocket", "urllib3.util.ssltransport.SSLTransport"], "uses_pep604_syntax": true}, "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["ssl.SSLSocket", "urllib3.util.ssltransport.SSLTransport", "builtins.bool"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, "__annotations__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "urllib3.connection._WrappedAndVerifiedSocket.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "urllib3.connection._WrappedAndVerifiedSocket.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "urllib3.connection._WrappedAndVerifiedSocket.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "socket"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "is_verified"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "__new__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["_cls", "socket", "is_verified"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static"], "fullname": "urllib3.connection._WrappedAndVerifiedSocket.__new__", "name": "__new__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["_cls", "socket", "is_verified"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "urllib3.connection._WrappedAndVerifiedSocket._NT", "id": -1, "name": "_NT", "namespace": "urllib3.connection._WrappedAndVerifiedSocket.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": ["ssl.SSLSocket", "urllib3.util.ssltransport.SSLTransport"], "uses_pep604_syntax": true}, "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["ssl.SSLSocket", "urllib3.util.ssltransport.SSLTransport", "builtins.bool"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "UnionType", "items": ["ssl.SSLSocket", "urllib3.util.ssltransport.SSLTransport"], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of _WrappedAndVerifiedSocket", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "urllib3.connection._WrappedAndVerifiedSocket._NT", "id": -1, "name": "_NT", "namespace": "urllib3.connection._WrappedAndVerifiedSocket.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": ["ssl.SSLSocket", "urllib3.util.ssltransport.SSLTransport"], "uses_pep604_syntax": true}, "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["ssl.SSLSocket", "urllib3.util.ssltransport.SSLTransport", "builtins.bool"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "urllib3.connection._WrappedAndVerifiedSocket._NT", "id": -1, "name": "_NT", "namespace": "urllib3.connection._WrappedAndVerifiedSocket.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": ["ssl.SSLSocket", "urllib3.util.ssltransport.SSLTransport"], "uses_pep604_syntax": true}, "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["ssl.SSLSocket", "urllib3.util.ssltransport.SSLTransport", "builtins.bool"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_asdict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["_self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "urllib3.connection._WrappedAndVerifiedSocket._asdict", "name": "_asdict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["_self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "urllib3.connection._WrappedAndVerifiedSocket._NT", "id": -1, "name": "_NT", "namespace": "urllib3.connection._WrappedAndVerifiedSocket._asdict", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": ["ssl.SSLSocket", "urllib3.util.ssltransport.SSLTransport"], "uses_pep604_syntax": true}, "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["ssl.SSLSocket", "urllib3.util.ssltransport.SSLTransport", "builtins.bool"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_asdict of _WrappedAndVerifiedSocket", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "urllib3.connection._WrappedAndVerifiedSocket._NT", "id": -1, "name": "_NT", "namespace": "urllib3.connection._WrappedAndVerifiedSocket._asdict", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": ["ssl.SSLSocket", "urllib3.util.ssltransport.SSLTransport"], "uses_pep604_syntax": true}, "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["ssl.SSLSocket", "urllib3.util.ssltransport.SSLTransport", "builtins.bool"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_field_defaults": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "urllib3.connection._WrappedAndVerifiedSocket._field_defaults", "name": "_field_defaults", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_field_types": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "urllib3.connection._WrappedAndVerifiedSocket._field_types", "name": "_field_types", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "urllib3.connection._WrappedAndVerifiedSocket._fields", "name": "_fields", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_make": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "urllib3.connection._WrappedAndVerifiedSocket._make", "name": "_make", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "urllib3.connection._WrappedAndVerifiedSocket._NT", "id": -1, "name": "_NT", "namespace": "urllib3.connection._WrappedAndVerifiedSocket._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": ["ssl.SSLSocket", "urllib3.util.ssltransport.SSLTransport"], "uses_pep604_syntax": true}, "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["ssl.SSLSocket", "urllib3.util.ssltransport.SSLTransport", "builtins.bool"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make of _WrappedAndVerifiedSocket", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "urllib3.connection._WrappedAndVerifiedSocket._NT", "id": -1, "name": "_NT", "namespace": "urllib3.connection._WrappedAndVerifiedSocket._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": ["ssl.SSLSocket", "urllib3.util.ssltransport.SSLTransport"], "uses_pep604_syntax": true}, "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["ssl.SSLSocket", "urllib3.util.ssltransport.SSLTransport", "builtins.bool"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "urllib3.connection._WrappedAndVerifiedSocket._NT", "id": -1, "name": "_NT", "namespace": "urllib3.connection._WrappedAndVerifiedSocket._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": ["ssl.SSLSocket", "urllib3.util.ssltransport.SSLTransport"], "uses_pep604_syntax": true}, "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["ssl.SSLSocket", "urllib3.util.ssltransport.SSLTransport", "builtins.bool"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_classmethod", "is_ready"], "fullname": "urllib3.connection._WrappedAndVerifiedSocket._make", "name": "_make", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "urllib3.connection._WrappedAndVerifiedSocket._NT", "id": -1, "name": "_NT", "namespace": "urllib3.connection._WrappedAndVerifiedSocket._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": ["ssl.SSLSocket", "urllib3.util.ssltransport.SSLTransport"], "uses_pep604_syntax": true}, "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["ssl.SSLSocket", "urllib3.util.ssltransport.SSLTransport", "builtins.bool"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make of _WrappedAndVerifiedSocket", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "urllib3.connection._WrappedAndVerifiedSocket._NT", "id": -1, "name": "_NT", "namespace": "urllib3.connection._WrappedAndVerifiedSocket._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": ["ssl.SSLSocket", "urllib3.util.ssltransport.SSLTransport"], "uses_pep604_syntax": true}, "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["ssl.SSLSocket", "urllib3.util.ssltransport.SSLTransport", "builtins.bool"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "urllib3.connection._WrappedAndVerifiedSocket._NT", "id": -1, "name": "_NT", "namespace": "urllib3.connection._WrappedAndVerifiedSocket._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": ["ssl.SSLSocket", "urllib3.util.ssltransport.SSLTransport"], "uses_pep604_syntax": true}, "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["ssl.SSLSocket", "urllib3.util.ssltransport.SSLTransport", "builtins.bool"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}}, "plugin_generated": true}, "_replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5], "arg_names": ["_self", "socket", "is_verified"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "urllib3.connection._WrappedAndVerifiedSocket._replace", "name": "_replace", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5], "arg_names": ["_self", "socket", "is_verified"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "urllib3.connection._WrappedAndVerifiedSocket._NT", "id": -1, "name": "_NT", "namespace": "urllib3.connection._WrappedAndVerifiedSocket._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": ["ssl.SSLSocket", "urllib3.util.ssltransport.SSLTransport"], "uses_pep604_syntax": true}, "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["ssl.SSLSocket", "urllib3.util.ssltransport.SSLTransport", "builtins.bool"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, {".class": "UnionType", "items": ["ssl.SSLSocket", "urllib3.util.ssltransport.SSLTransport"], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_replace of _WrappedAndVerifiedSocket", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "urllib3.connection._WrappedAndVerifiedSocket._NT", "id": -1, "name": "_NT", "namespace": "urllib3.connection._WrappedAndVerifiedSocket._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": ["ssl.SSLSocket", "urllib3.util.ssltransport.SSLTransport"], "uses_pep604_syntax": true}, "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["ssl.SSLSocket", "urllib3.util.ssltransport.SSLTransport", "builtins.bool"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "urllib3.connection._WrappedAndVerifiedSocket._NT", "id": -1, "name": "_NT", "namespace": "urllib3.connection._WrappedAndVerifiedSocket._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": ["ssl.SSLSocket", "urllib3.util.ssltransport.SSLTransport"], "uses_pep604_syntax": true}, "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["ssl.SSLSocket", "urllib3.util.ssltransport.SSLTransport", "builtins.bool"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_source": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "urllib3.connection._WrappedAndVerifiedSocket._source", "name": "_source", "type": "builtins.str"}}, "is_verified": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "urllib3.connection._WrappedAndVerifiedSocket.is_verified", "name": "is_verified", "type": "builtins.bool"}}, "is_verified-redefinition": {".class": "SymbolTableNode", "cross_ref": "urllib3.connection._WrappedAndVerifiedSocket.is_verified", "kind": "<PERSON><PERSON><PERSON>"}, "socket": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "urllib3.connection._WrappedAndVerifiedSocket.socket", "name": "socket", "type": {".class": "UnionType", "items": ["ssl.SSLSocket", "urllib3.util.ssltransport.SSLTransport"], "uses_pep604_syntax": true}}}, "socket-redefinition": {".class": "SymbolTableNode", "cross_ref": "urllib3.connection._WrappedAndVerifiedSocket.socket", "kind": "<PERSON><PERSON><PERSON>"}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "urllib3.connection._WrappedAndVerifiedSocket.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": ["ssl.SSLSocket", "urllib3.util.ssltransport.SSLTransport"], "uses_pep604_syntax": true}, "builtins.bool"], "partial_fallback": "urllib3.connection._WrappedAndVerifiedSocket"}, "values": [], "variance": 0}, "slots": null, "tuple_type": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": ["ssl.SSLSocket", "urllib3.util.ssltransport.SSLTransport"], "uses_pep604_syntax": true}, "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["ssl.SSLSocket", "urllib3.util.ssltransport.SSLTransport", "builtins.bool"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "urllib3.connection.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "urllib3.connection.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "urllib3.connection.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "urllib3.connection.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "urllib3.connection.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "urllib3.connection.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "__version__": {".class": "SymbolTableNode", "cross_ref": "urllib3._version.__version__", "kind": "Gdef"}, "_assert_fingerprint": {".class": "SymbolTableNode", "cross_ref": "urllib3.util.ssl_.assert_fingerprint", "kind": "Gdef"}, "_get_default_user_agent": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "urllib3.connection._get_default_user_agent", "name": "_get_default_user_agent", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_default_user_agent", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_match_hostname": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["cert", "asserted_hostname", "hostname_checks_common_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "urllib3.connection._match_hostname", "name": "_match_hostname", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["cert", "asserted_hostname", "hostname_checks_common_name"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "urllib3.util.ssl_._TYPE_PEER_CERT_RET_DICT"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_match_hostname", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_ssl_wrap_socket_and_match_hostname": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 5], "arg_names": ["sock", "cert_reqs", "ssl_version", "ssl_minimum_version", "ssl_maximum_version", "cert_file", "key_file", "key_password", "ca_certs", "ca_cert_dir", "ca_cert_data", "assert_hostname", "assert_fingerprint", "server_hostname", "ssl_context", "tls_in_tls"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "urllib3.connection._ssl_wrap_socket_and_match_hostname", "name": "_ssl_wrap_socket_and_match_hostname", "type": {".class": "CallableType", "arg_kinds": [0, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 5], "arg_names": ["sock", "cert_reqs", "ssl_version", "ssl_minimum_version", "ssl_maximum_version", "cert_file", "key_file", "key_password", "ca_certs", "ca_cert_dir", "ca_cert_data", "assert_hostname", "assert_fingerprint", "server_hostname", "ssl_context", "tls_in_tls"], "arg_types": ["socket.socket", {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str", "builtins.int"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str", "builtins.int"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str", {".class": "LiteralType", "fallback": "builtins.bool", "value": false}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["ssl.SSLContext", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_ssl_wrap_socket_and_match_hostname", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "urllib3.connection._WrappedAndVerifiedSocket"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_url_from_connection": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["conn", "path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "urllib3.connection._url_from_connection", "name": "_url_from_connection", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["conn", "path"], "arg_types": [{".class": "UnionType", "items": ["urllib3.connection.HTTPConnection", "urllib3.connection.HTTPSConnection"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_url_from_connection", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_wrap_proxy_error": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["err", "proxy_scheme"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "urllib3.connection._wrap_proxy_error", "name": "_wrap_proxy_error", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["err", "proxy_scheme"], "arg_types": ["builtins.Exception", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_wrap_proxy_error", "ret_type": "urllib3.exceptions.ProxyError", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "assert_header_parsing": {".class": "SymbolTableNode", "cross_ref": "urllib3.util.response.assert_header_parsing", "kind": "Gdef"}, "body_to_chunks": {".class": "SymbolTableNode", "cross_ref": "urllib3.util.request.body_to_chunks", "kind": "Gdef"}, "connection": {".class": "SymbolTableNode", "cross_ref": "urllib3.util.connection", "kind": "Gdef"}, "create_urllib3_context": {".class": "SymbolTableNode", "cross_ref": "urllib3.util.ssl_.create_urllib3_context", "kind": "Gdef"}, "datetime": {".class": "SymbolTableNode", "cross_ref": "datetime", "kind": "Gdef"}, "http": {".class": "SymbolTableNode", "cross_ref": "http", "kind": "Gdef"}, "http2_probe": {".class": "SymbolTableNode", "cross_ref": "urllib3.http2.probe", "kind": "Gdef"}, "is_ipaddress": {".class": "SymbolTableNode", "cross_ref": "urllib3.util.ssl_.is_ipaddress", "kind": "Gdef"}, "log": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "urllib3.connection.log", "name": "log", "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "match_hostname": {".class": "SymbolTableNode", "cross_ref": "urllib3.util.ssl_match_hostname.match_hostname", "kind": "Gdef"}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "port_by_scheme": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "urllib3.connection.port_by_scheme", "name": "port_by_scheme", "type": {".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef"}, "resolve_cert_reqs": {".class": "SymbolTableNode", "cross_ref": "urllib3.util.ssl_.resolve_cert_reqs", "kind": "Gdef"}, "resolve_ssl_version": {".class": "SymbolTableNode", "cross_ref": "urllib3.util.ssl_.resolve_ssl_version", "kind": "Gdef"}, "socket": {".class": "SymbolTableNode", "cross_ref": "socket", "kind": "Gdef"}, "ssl": {".class": "SymbolTableNode", "cross_ref": "ssl", "kind": "Gdef"}, "ssl_": {".class": "SymbolTableNode", "cross_ref": "urllib3.util.ssl_", "kind": "Gdef"}, "ssl_wrap_socket": {".class": "SymbolTableNode", "cross_ref": "urllib3.util.ssl_.ssl_wrap_socket", "kind": "Gdef"}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef"}, "threading": {".class": "SymbolTableNode", "cross_ref": "threading", "kind": "Gdef"}, "to_str": {".class": "SymbolTableNode", "cross_ref": "urllib3.util.util.to_str", "kind": "Gdef"}, "typing": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef"}, "wait_for_read": {".class": "SymbolTableNode", "cross_ref": "urllib3.util.wait.wait_for_read", "kind": "Gdef"}, "warnings": {".class": "SymbolTableNode", "cross_ref": "warnings", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\urllib3\\connection.py"}