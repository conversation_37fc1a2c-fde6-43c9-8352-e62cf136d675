{"data_mtime": 1757882372, "dep_lines": [18, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 30, 30, 30, 30], "dependencies": ["selenium.webdriver.remote.remote_connection", "builtins", "_frozen_importlib", "abc", "selenium.webdriver.remote", "typing"], "hash": "c796de04a580542c1e18fd91402ce4f9565997fa", "id": "selenium.webdriver.chromium.remote_connection", "ignore_all": true, "interface_hash": "3b2756f79c449021c20e90cc9f7f544a5a442bc8", "mtime": 1757882171, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\selenium\\webdriver\\chromium\\remote_connection.py", "plugin_data": null, "size": 2651, "suppressed": [], "version_id": "1.15.0"}