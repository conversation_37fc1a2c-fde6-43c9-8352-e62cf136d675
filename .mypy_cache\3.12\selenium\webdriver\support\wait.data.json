{".class": "MypyFile", "_fullname": "selenium.webdriver.support.wait", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "IGNORED_EXCEPTIONS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "selenium.webdriver.support.wait.IGNORED_EXCEPTIONS", "name": "IGNORED_EXCEPTIONS", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeType", "item": "builtins.Exception"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef"}, "NoSuchElementException": {".class": "SymbolTableNode", "cross_ref": "selenium.common.exceptions.NoSuchElementException", "kind": "Gdef"}, "POLL_FREQUENCY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "selenium.webdriver.support.wait.POLL_FREQUENCY", "name": "POLL_FREQUENCY", "type": "builtins.float"}}, "T": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "selenium.webdriver.support.wait.T", "name": "T", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "TimeoutException": {".class": "SymbolTableNode", "cross_ref": "selenium.common.exceptions.TimeoutException", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "WaitExcTypes": {".class": "SymbolTableNode", "cross_ref": "selenium.types.WaitExcTypes", "kind": "Gdef"}, "WebDriver": {".class": "SymbolTableNode", "cross_ref": "selenium.webdriver.remote.webdriver.WebDriver", "kind": "Gdef"}, "WebDriverWait": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "selenium.webdriver.support.wait.WebDriverWait", "name": "WebDriverWait", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "selenium.webdriver.support.wait.WebDriverWait", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "selenium.webdriver.support.wait", "mro": ["selenium.webdriver.support.wait.WebDriverWait", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "driver", "timeout", "poll_frequency", "ignored_exceptions"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "selenium.webdriver.support.wait.WebDriverWait.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "driver", "timeout", "poll_frequency", "ignored_exceptions"], "arg_types": ["selenium.webdriver.support.wait.WebDriverWait", "selenium.webdriver.remote.webdriver.WebDriver", "builtins.float", "builtins.float", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "selenium.types.WaitExcTypes"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of WebDriverWait", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "selenium.webdriver.support.wait.WebDriverWait.__repr__", "name": "__repr__", "type": null}}, "_driver": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "selenium.webdriver.support.wait.WebDriverWait._driver", "name": "_driver", "type": "selenium.webdriver.remote.webdriver.WebDriver"}}, "_ignored_exceptions": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "selenium.webdriver.support.wait.WebDriverWait._ignored_exceptions", "name": "_ignored_exceptions", "type": {".class": "Instance", "args": [{".class": "TypeType", "item": "builtins.Exception"}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "_poll": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "selenium.webdriver.support.wait.WebDriverWait._poll", "name": "_poll", "type": "builtins.float"}}, "_timeout": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "selenium.webdriver.support.wait.WebDriverWait._timeout", "name": "_timeout", "type": "builtins.float"}}, "until": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "method", "message"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "selenium.webdriver.support.wait.WebDriverWait.until", "name": "until", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "method", "message"], "arg_types": ["selenium.webdriver.support.wait.WebDriverWait", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["selenium.webdriver.remote.webdriver.WebDriver"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "selenium.webdriver.support.wait.T", "id": -1, "name": "T", "namespace": "selenium.webdriver.support.wait.WebDriverWait.until", "upper_bound": "builtins.object", "values": [], "variance": 0}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "until of WebDriverWait", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "selenium.webdriver.support.wait.T", "id": -1, "name": "T", "namespace": "selenium.webdriver.support.wait.WebDriverWait.until", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "selenium.webdriver.support.wait.T", "id": -1, "name": "T", "namespace": "selenium.webdriver.support.wait.WebDriverWait.until", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "until_not": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "method", "message"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "selenium.webdriver.support.wait.WebDriverWait.until_not", "name": "until_not", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "method", "message"], "arg_types": ["selenium.webdriver.support.wait.WebDriverWait", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["selenium.webdriver.remote.webdriver.WebDriver"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "selenium.webdriver.support.wait.T", "id": -1, "name": "T", "namespace": "selenium.webdriver.support.wait.WebDriverWait.until_not", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "until_not of WebDriverWait", "ret_type": {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "selenium.webdriver.support.wait.T", "id": -1, "name": "T", "namespace": "selenium.webdriver.support.wait.WebDriverWait.until_not", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "selenium.webdriver.support.wait.T", "id": -1, "name": "T", "namespace": "selenium.webdriver.support.wait.WebDriverWait.until_not", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "selenium.webdriver.support.wait.WebDriverWait.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "selenium.webdriver.support.wait.WebDriverWait", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "selenium.webdriver.support.wait.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "selenium.webdriver.support.wait.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "selenium.webdriver.support.wait.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "selenium.webdriver.support.wait.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "selenium.webdriver.support.wait.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "selenium.webdriver.support.wait.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "time": {".class": "SymbolTableNode", "cross_ref": "time", "kind": "Gdef"}, "typing": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\selenium\\webdriver\\support\\wait.py"}