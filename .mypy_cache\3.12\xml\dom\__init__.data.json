{".class": "MypyFile", "_fullname": "xml.dom", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "DOMException": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.Exception"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "xml.dom.DOMException", "name": "DOMException", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "xml.dom.DOMException", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "xml.dom", "mro": ["xml.dom.DOMException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "xml.dom.DOMException.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kw"], "arg_types": ["xml.dom.DOMException", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of DOMException", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "xml.dom.DOMException._get_code", "name": "_get_code", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["xml.dom.DOMException"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_code of DOMException", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "xml.dom.DOMException.code", "name": "code", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "xml.dom.DOMException.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "xml.dom.DOMException", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DOMSTRING_SIZE_ERR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "xml.dom.DOMSTRING_SIZE_ERR", "name": "DOMSTRING_SIZE_ERR", "type": "builtins.int"}}, "DomstringSizeErr": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["xml.dom.DOMException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "xml.dom.DomstringSizeErr", "name": "DomstringSizeErr", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "xml.dom.DomstringSizeErr", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "xml.dom", "mro": ["xml.dom.DomstringSizeErr", "xml.dom.DOMException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "xml.dom.DomstringSizeErr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "xml.dom.DomstringSizeErr", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "EMPTY_NAMESPACE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "xml.dom.EMPTY_NAMESPACE", "name": "EMPTY_NAMESPACE", "type": {".class": "NoneType"}}}, "EMPTY_PREFIX": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "xml.dom.EMPTY_PREFIX", "name": "EMPTY_PREFIX", "type": {".class": "NoneType"}}}, "Final": {".class": "SymbolTableNode", "cross_ref": "typing.Final", "kind": "Gdef", "module_hidden": true, "module_public": false}, "HIERARCHY_REQUEST_ERR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "xml.dom.HIERARCHY_REQUEST_ERR", "name": "HIERARCHY_REQUEST_ERR", "type": "builtins.int"}}, "HierarchyRequestErr": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["xml.dom.DOMException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "xml.dom.HierarchyRequestErr", "name": "HierarchyRequestErr", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "xml.dom.HierarchyRequestErr", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "xml.dom", "mro": ["xml.dom.HierarchyRequestErr", "xml.dom.DOMException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "xml.dom.HierarchyRequestErr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "xml.dom.HierarchyRequestErr", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "INDEX_SIZE_ERR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "xml.dom.INDEX_SIZE_ERR", "name": "INDEX_SIZE_ERR", "type": "builtins.int"}}, "INUSE_ATTRIBUTE_ERR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "xml.dom.INUSE_ATTRIBUTE_ERR", "name": "INUSE_ATTRIBUTE_ERR", "type": "builtins.int"}}, "INVALID_ACCESS_ERR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "xml.dom.INVALID_ACCESS_ERR", "name": "INVALID_ACCESS_ERR", "type": "builtins.int"}}, "INVALID_CHARACTER_ERR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "xml.dom.INVALID_CHARACTER_ERR", "name": "INVALID_CHARACTER_ERR", "type": "builtins.int"}}, "INVALID_MODIFICATION_ERR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "xml.dom.INVALID_MODIFICATION_ERR", "name": "INVALID_MODIFICATION_ERR", "type": "builtins.int"}}, "INVALID_STATE_ERR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "xml.dom.INVALID_STATE_ERR", "name": "INVALID_STATE_ERR", "type": "builtins.int"}}, "IndexSizeErr": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["xml.dom.DOMException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "xml.dom.IndexSizeErr", "name": "IndexSizeErr", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "xml.dom.IndexSizeErr", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "xml.dom", "mro": ["xml.dom.IndexSizeErr", "xml.dom.DOMException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "xml.dom.IndexSizeErr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "xml.dom.IndexSizeErr", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InuseAttributeErr": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["xml.dom.DOMException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "xml.dom.InuseAttributeErr", "name": "InuseAttributeErr", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "xml.dom.InuseAttributeErr", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "xml.dom", "mro": ["xml.dom.InuseAttributeErr", "xml.dom.DOMException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "xml.dom.InuseAttributeErr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "xml.dom.InuseAttributeErr", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InvalidAccessErr": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["xml.dom.DOMException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "xml.dom.InvalidAccessErr", "name": "InvalidAccessErr", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "xml.dom.InvalidAccessErr", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "xml.dom", "mro": ["xml.dom.InvalidAccessErr", "xml.dom.DOMException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "xml.dom.InvalidAccessErr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "xml.dom.InvalidAccessErr", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InvalidCharacterErr": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["xml.dom.DOMException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "xml.dom.InvalidCharacterErr", "name": "InvalidCharacterErr", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "xml.dom.InvalidCharacterErr", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "xml.dom", "mro": ["xml.dom.InvalidCharacterErr", "xml.dom.DOMException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "xml.dom.InvalidCharacterErr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "xml.dom.InvalidCharacterErr", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InvalidModificationErr": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["xml.dom.DOMException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "xml.dom.InvalidModificationErr", "name": "InvalidModificationErr", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "xml.dom.InvalidModificationErr", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "xml.dom", "mro": ["xml.dom.InvalidModificationErr", "xml.dom.DOMException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "xml.dom.InvalidModificationErr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "xml.dom.InvalidModificationErr", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InvalidStateErr": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["xml.dom.DOMException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "xml.dom.InvalidStateErr", "name": "InvalidStateErr", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "xml.dom.InvalidStateErr", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "xml.dom", "mro": ["xml.dom.InvalidStateErr", "xml.dom.DOMException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "xml.dom.InvalidStateErr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "xml.dom.InvalidStateErr", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NAMESPACE_ERR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "xml.dom.NAMESPACE_ERR", "name": "NAMESPACE_ERR", "type": "builtins.int"}}, "NOT_FOUND_ERR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "xml.dom.NOT_FOUND_ERR", "name": "NOT_FOUND_ERR", "type": "builtins.int"}}, "NOT_SUPPORTED_ERR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "xml.dom.NOT_SUPPORTED_ERR", "name": "NOT_SUPPORTED_ERR", "type": "builtins.int"}}, "NO_DATA_ALLOWED_ERR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "xml.dom.NO_DATA_ALLOWED_ERR", "name": "NO_DATA_ALLOWED_ERR", "type": "builtins.int"}}, "NO_MODIFICATION_ALLOWED_ERR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "xml.dom.NO_MODIFICATION_ALLOWED_ERR", "name": "NO_MODIFICATION_ALLOWED_ERR", "type": "builtins.int"}}, "NamespaceErr": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["xml.dom.DOMException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "xml.dom.NamespaceErr", "name": "NamespaceErr", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "xml.dom.NamespaceErr", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "xml.dom", "mro": ["xml.dom.NamespaceErr", "xml.dom.DOMException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "xml.dom.NamespaceErr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "xml.dom.NamespaceErr", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NoDataAllowedErr": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["xml.dom.DOMException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "xml.dom.NoDataAllowedErr", "name": "NoDataAllowedErr", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "xml.dom.NoDataAllowedErr", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "xml.dom", "mro": ["xml.dom.NoDataAllowedErr", "xml.dom.DOMException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "xml.dom.NoDataAllowedErr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "xml.dom.NoDataAllowedErr", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NoModificationAllowedErr": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["xml.dom.DOMException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "xml.dom.NoModificationAllowedErr", "name": "NoModificationAllowedErr", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "xml.dom.NoModificationAllowedErr", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "xml.dom", "mro": ["xml.dom.NoModificationAllowedErr", "xml.dom.DOMException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "xml.dom.NoModificationAllowedErr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "xml.dom.NoModificationAllowedErr", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Node": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "xml.dom.Node", "name": "Node", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "xml.dom.Node", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "xml.dom", "mro": ["xml.dom.Node", "builtins.object"], "names": {".class": "SymbolTable", "ATTRIBUTE_NODE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "xml.dom.Node.ATTRIBUTE_NODE", "name": "ATTRIBUTE_NODE", "type": "builtins.int"}}, "CDATA_SECTION_NODE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "xml.dom.Node.CDATA_SECTION_NODE", "name": "CDATA_SECTION_NODE", "type": "builtins.int"}}, "COMMENT_NODE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "xml.dom.Node.COMMENT_NODE", "name": "COMMENT_NODE", "type": "builtins.int"}}, "DOCUMENT_FRAGMENT_NODE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "xml.dom.Node.DOCUMENT_FRAGMENT_NODE", "name": "DOCUMENT_FRAGMENT_NODE", "type": "builtins.int"}}, "DOCUMENT_NODE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "xml.dom.Node.DOCUMENT_NODE", "name": "DOCUMENT_NODE", "type": "builtins.int"}}, "DOCUMENT_TYPE_NODE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "xml.dom.Node.DOCUMENT_TYPE_NODE", "name": "DOCUMENT_TYPE_NODE", "type": "builtins.int"}}, "ELEMENT_NODE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "xml.dom.Node.ELEMENT_NODE", "name": "ELEMENT_NODE", "type": "builtins.int"}}, "ENTITY_NODE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "xml.dom.Node.ENTITY_NODE", "name": "ENTITY_NODE", "type": "builtins.int"}}, "ENTITY_REFERENCE_NODE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "xml.dom.Node.ENTITY_REFERENCE_NODE", "name": "ENTITY_REFERENCE_NODE", "type": "builtins.int"}}, "NOTATION_NODE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "xml.dom.Node.NOTATION_NODE", "name": "NOTATION_NODE", "type": "builtins.int"}}, "PROCESSING_INSTRUCTION_NODE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "xml.dom.Node.PROCESSING_INSTRUCTION_NODE", "name": "PROCESSING_INSTRUCTION_NODE", "type": "builtins.int"}}, "TEXT_NODE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "xml.dom.Node.TEXT_NODE", "name": "TEXT_NODE", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "xml.dom.Node.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "xml.dom.Node", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NotFoundErr": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["xml.dom.DOMException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "xml.dom.NotFoundErr", "name": "NotFoundErr", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "xml.dom.NotFoundErr", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "xml.dom", "mro": ["xml.dom.NotFoundErr", "xml.dom.DOMException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "xml.dom.NotFoundErr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "xml.dom.NotFoundErr", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NotSupportedErr": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["xml.dom.DOMException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "xml.dom.NotSupportedErr", "name": "NotSupportedErr", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "xml.dom.NotSupportedErr", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "xml.dom", "mro": ["xml.dom.NotSupportedErr", "xml.dom.DOMException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "xml.dom.NotSupportedErr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "xml.dom.NotSupportedErr", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SYNTAX_ERR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "xml.dom.SYNTAX_ERR", "name": "SYNTAX_ERR", "type": "builtins.int"}}, "SyntaxErr": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["xml.dom.DOMException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "xml.dom.SyntaxErr", "name": "SyntaxErr", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "xml.dom.SyntaxErr", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "xml.dom", "mro": ["xml.dom.SyntaxErr", "xml.dom.DOMException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "xml.dom.SyntaxErr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "xml.dom.SyntaxErr", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "UserDataHandler": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "xml.dom.UserDataHandler", "name": "UserDataHandler", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "xml.dom.UserDataHandler", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "xml.dom", "mro": ["xml.dom.UserDataHandler", "builtins.object"], "names": {".class": "SymbolTable", "NODE_CLONED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "xml.dom.UserDataHandler.NODE_CLONED", "name": "NODE_CLONED", "type": "builtins.int"}}, "NODE_DELETED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "xml.dom.UserDataHandler.NODE_DELETED", "name": "NODE_DELETED", "type": "builtins.int"}}, "NODE_IMPORTED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "xml.dom.UserDataHandler.NODE_IMPORTED", "name": "NODE_IMPORTED", "type": "builtins.int"}}, "NODE_RENAMED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "xml.dom.UserDataHandler.NODE_RENAMED", "name": "NODE_RENAMED", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "xml.dom.UserDataHandler.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "xml.dom.UserDataHandler", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "VALIDATION_ERR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "xml.dom.VALIDATION_ERR", "name": "VALIDATION_ERR", "type": "builtins.int"}}, "ValidationErr": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["xml.dom.DOMException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "xml.dom.ValidationErr", "name": "ValidationErr", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "xml.dom.ValidationErr", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "xml.dom", "mro": ["xml.dom.ValidationErr", "xml.dom.DOMException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "xml.dom.ValidationErr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "xml.dom.ValidationErr", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "WRONG_DOCUMENT_ERR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "xml.dom.WRONG_DOCUMENT_ERR", "name": "WRONG_DOCUMENT_ERR", "type": "builtins.int"}}, "WrongDocumentErr": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["xml.dom.DOMException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "xml.dom.WrongDocumentErr", "name": "WrongDocumentErr", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "xml.dom.WrongDocumentErr", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "xml.dom", "mro": ["xml.dom.WrongDocumentErr", "xml.dom.DOMException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "xml.dom.WrongDocumentErr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "xml.dom.WrongDocumentErr", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "XHTML_NAMESPACE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "xml.dom.XHTML_NAMESPACE", "name": "XHTML_NAMESPACE", "type": "builtins.str"}}, "XMLNS_NAMESPACE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "xml.dom.XMLNS_NAMESPACE", "name": "XMLNS_NAMESPACE", "type": "builtins.str"}}, "XML_NAMESPACE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "xml.dom.XML_NAMESPACE", "name": "XML_NAMESPACE", "type": "builtins.str"}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "xml.dom.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "xml.dom.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "xml.dom.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "xml.dom.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "xml.dom.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "xml.dom.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "xml.dom.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "getDOMImplementation": {".class": "SymbolTableNode", "cross_ref": "xml.dom.domreg.getDOMImplementation", "kind": "Gdef"}, "registerDOMImplementation": {".class": "SymbolTableNode", "cross_ref": "xml.dom.domreg.registerDOMImplementation", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.mypy-type-checker-2025.2.0\\bundled\\libs\\mypy\\typeshed\\stdlib\\xml\\dom\\__init__.pyi"}